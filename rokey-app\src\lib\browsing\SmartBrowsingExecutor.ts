// Smart Browsing Executor - Intelligent plan-based browsing with todo list management
// Handles complex browsing tasks by creating plans, executing subtasks, and updating progress

import BrowserlessService from '@/lib/browserless';
import { BrowsingModel } from '@/types/customApiConfigs';

export interface BrowsingSubtask {
  id: string;
  type: 'search' | 'navigate' | 'extract' | 'analyze';
  description: string;
  query: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  priority: number; // 1-10, higher = more important
  dependencies?: string[]; // IDs of subtasks that must complete first
  result?: any;
  error?: string;
  attempts: number;
  maxAttempts: number;
  searchTerms?: string[]; // Alternative search terms to try
  expectedInfo?: string; // What information we expect to find
}

export interface BrowsingPlan {
  id: string;
  originalQuery: string;
  goal: string;
  subtasks: BrowsingSubtask[];
  status: 'planning' | 'executing' | 'completed' | 'failed';
  progress: number; // 0-100
  gatheredData: Record<string, any>;
  visitedUrls: string[];
  searchQueries: string[];
  completedSubtasks: string[];
  failedSubtasks: string[];
  currentSubtask?: string;
  finalResult?: string;
  createdAt: string;
  updatedAt: string;
  sessionInfo?: {
    sessionId: string;
    reconnectUrl: string;
    lastUsed: Date;
    activeWorkflow?: boolean;
  };
}

interface BrowsingConfig {
  browsing_enabled: boolean;
  browsing_models: BrowsingModel[];
}

export interface BrowsingProgressCallback {
  onPlanCreated?: (plan: BrowsingPlan) => void;
  onTaskStarted?: (task: BrowsingSubtask, plan: BrowsingPlan) => void;
  onTaskCompleted?: (task: BrowsingSubtask, plan: BrowsingPlan) => void;
  onTaskFailed?: (task: BrowsingSubtask, plan: BrowsingPlan) => void;
  onProgressUpdate?: (progress: number, plan: BrowsingPlan) => void;
  onStatusUpdate?: (status: string, plan: BrowsingPlan) => void;
  onPlanCompleted?: (plan: BrowsingPlan) => void;
}

export class SmartBrowsingExecutor {
  private static instance: SmartBrowsingExecutor;
  private browserlessService: BrowserlessService;
  private activePlans: Map<string, BrowsingPlan> = new Map();

  constructor() {
    this.browserlessService = BrowserlessService.getInstance();
  }

  static getInstance(): SmartBrowsingExecutor {
    if (!SmartBrowsingExecutor.instance) {
      SmartBrowsingExecutor.instance = new SmartBrowsingExecutor();
    }
    return SmartBrowsingExecutor.instance;
  }

  /**
   * Execute smart browsing with planning and todo list management
   */
  async executeSmartBrowsing(
    query: string,
    browsingConfig: BrowsingConfig,
    browsingType: 'search' | 'navigate' | 'extract' = 'search',
    progressCallback?: BrowsingProgressCallback
  ): Promise<{ success: boolean; content?: string; error?: string; plan?: BrowsingPlan }> {
    try {
      console.log(`[Smart Browsing] 🎯 Starting intelligent browsing for: "${query}"`);

      // Step 1: Create a browsing plan
      const plan = await this.createBrowsingPlan(query, browsingType, browsingConfig);
      this.activePlans.set(plan.id, plan);

      console.log(`[Smart Browsing] 📋 Created plan with ${plan.subtasks.length} subtasks`);
      this.logPlan(plan);

      // Notify plan creation
      progressCallback?.onPlanCreated?.(plan);

      // Step 2: Execute the plan
      const result = await this.executePlan(plan, browsingConfig, progressCallback);

      if (result.success) {
        console.log(`[Smart Browsing] ✅ Plan completed successfully`);
        return {
          success: true,
          content: result.content,
          plan: plan
        };
      } else {
        console.log(`[Smart Browsing] ❌ Plan failed: ${result.error}`);

        // Check if this is a network connectivity issue
        if (this.isNetworkError(result.error)) {
          console.log(`[Smart Browsing] 🌐 Network connectivity issue detected, recommending fallback to simple browsing`);
          return {
            success: false,
            error: `Network connectivity issue: ${result.error}. Falling back to simple browsing.`,
            plan: plan,
            shouldFallback: true
          };
        }

        return {
          success: false,
          error: result.error,
          plan: plan
        };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('[Smart Browsing] Fatal error:', errorMessage);

      // Check if this is a network connectivity issue
      if (this.isNetworkError(errorMessage)) {
        console.log(`[Smart Browsing] 🌐 Network connectivity issue detected, recommending fallback to simple browsing`);
        return {
          success: false,
          error: `Network connectivity issue: ${errorMessage}. Falling back to simple browsing.`,
          shouldFallback: true
        };
      }

      return {
        success: false,
        error: `Smart browsing failed: ${errorMessage}`
      };
    }
  }

  /**
   * Check if an error is related to network connectivity
   */
  private isNetworkError(errorMessage: string): boolean {
    const networkErrorPatterns = [
      'fetch failed',
      'ECONNRESET',
      'ENOTFOUND',
      'ETIMEDOUT',
      'ECONNREFUSED',
      'Network request failed',
      'Connection timeout',
      'DNS resolution failed'
    ];

    return networkErrorPatterns.some(pattern =>
      errorMessage.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  /**
   * Create an intelligent browsing plan based on the query
   */
  private async createBrowsingPlan(
    query: string,
    browsingType: string,
    browsingConfig: BrowsingConfig
  ): Promise<BrowsingPlan> {
    const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Use AI to create a smart plan
    const planningResult = await this.generatePlanWithAI(query, browsingType, browsingConfig);
    
    const plan: BrowsingPlan = {
      id: planId,
      originalQuery: query,
      goal: planningResult.goal || `Find comprehensive information about: ${query}`,
      subtasks: planningResult.subtasks || this.createFallbackPlan(query, browsingType),
      status: 'planning',
      progress: 0,
      gatheredData: {},
      visitedUrls: [],
      searchQueries: [],
      completedSubtasks: [],
      failedSubtasks: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return plan;
  }

  /**
   * Generate a browsing plan using AI
   */
  private async generatePlanWithAI(
    query: string,
    browsingType: string,
    browsingConfig: BrowsingConfig
  ): Promise<{ goal: string; subtasks: BrowsingSubtask[] }> {
    try {
      const model = browsingConfig.browsing_models[0]; // Use first available model for planning
      if (!model) {
        throw new Error('No browsing models available for planning');
      }

      const planningPrompt = this.buildPlanningPrompt(query, browsingType);
      const aiResult = await this.callAIForPlanning(planningPrompt, model);

      if (aiResult.success && aiResult.content) {
        return this.parsePlanFromAI(aiResult.content, query);
      } else {
        console.warn('[Smart Browsing] AI planning failed, using fallback plan');
        return {
          goal: `Find information about: ${query}`,
          subtasks: this.createFallbackPlan(query, browsingType)
        };
      }
    } catch (error) {
      console.warn('[Smart Browsing] AI planning error, using fallback:', error);
      return {
        goal: `Find information about: ${query}`,
        subtasks: this.createFallbackPlan(query, browsingType)
      };
    }
  }

  /**
   * Build a comprehensive planning prompt for AI
   */
  private buildPlanningPrompt(query: string, browsingType: string): string {
    // Get current date and time for context
    const now = new Date();
    const currentDateTime = now.toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });
    const currentYear = now.getFullYear();
    const currentMonth = now.toLocaleString('en-US', { month: 'long' });

    return `You are an expert web browsing strategist. Create a detailed browsing plan to thoroughly research this query: "${query}"

CURRENT DATE & TIME: ${currentDateTime}
CURRENT YEAR: ${currentYear}
CURRENT MONTH: ${currentMonth}

BROWSING TYPE: ${browsingType}

IMPORTANT: When generating search terms and subtasks, consider the current date and time context:
- For recent events, include "${currentYear}" in search terms
- For current trends, use "latest", "recent", "${currentMonth} ${currentYear}"
- For news queries, prioritize recent timeframes
- For technology/AI topics, include current year for latest developments
- For market/business queries, focus on current and recent data

Create a JSON response with this structure:
{
  "goal": "Clear statement of what we want to achieve",
  "subtasks": [
    {
      "id": "unique_id",
      "type": "search|navigate|extract|analyze",
      "description": "What this subtask does",
      "query": "Specific search query or URL",
      "priority": 1-10,
      "searchTerms": ["alternative", "search", "terms"],
      "expectedInfo": "What information we expect to find"
    }
  ]
}

GUIDELINES:
1. Start with broad searches, then get more specific
2. Use multiple search strategies and terms with temporal context
3. Include fact-checking and verification steps
4. Plan for 3-7 subtasks maximum
5. Make search terms diverse, comprehensive, and time-aware
6. Consider different angles and perspectives
7. Include analysis steps to synthesize information
8. For "navigate" tasks, only use if you have specific URLs (https://...)
9. Most tasks should be "search" type for better reliability
10. ALWAYS include temporal keywords when relevant:
    - For news: "latest news", "recent updates", "${currentMonth} ${currentYear}"
    - For trends: "current trends", "${currentYear} trends"
    - For technology: "latest developments", "${currentYear} updates"
    - For data/statistics: "current data", "recent statistics", "${currentYear} data"

Create a smart, thorough, and temporally-aware plan:`;
  }

  /**
   * Enhance query with temporal context for better search results
   */
  private enhanceQueryWithTemporal(query: string, temporalKeywords: string[]): {
    primary: string;
    temporal: string;
    alternatives: string[];
    recentTerms: string[];
  } {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().toLocaleString('en-US', { month: 'long' });

    // Detect if query already has temporal context
    const hasTemporalContext = /\b(latest|recent|current|new|today|\d{4}|now)\b/i.test(query);

    return {
      primary: query,
      temporal: hasTemporalContext ? query : `${query} ${currentYear} latest`,
      alternatives: [
        query,
        `${query} information`,
        `${query} details`,
        hasTemporalContext ? `${query} overview` : `${query} ${currentYear}`
      ],
      recentTerms: [
        `${query} recent`,
        `${query} latest news`,
        `${query} ${currentMonth} ${currentYear}`,
        `${query} current trends`
      ]
    };
  }

  /**
   * Create a fallback plan when AI planning fails
   */
  private createFallbackPlan(query: string, browsingType: string): BrowsingSubtask[] {
    const baseId = Date.now();
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().toLocaleString('en-US', { month: 'long' });

    // Add temporal context to search terms
    const temporalKeywords = [`${currentYear}`, `latest`, `recent`, `${currentMonth} ${currentYear}`];
    const enhancedQuery = this.enhanceQueryWithTemporal(query, temporalKeywords);
    
    return [
      {
        id: `search_${baseId}_1`,
        type: 'search',
        description: 'Primary search for main topic',
        query: enhancedQuery.primary,
        status: 'pending',
        priority: 10,
        attempts: 0,
        maxAttempts: 3,
        searchTerms: enhancedQuery.alternatives,
        expectedInfo: 'General information about the topic'
      },
      {
        id: `search_${baseId}_2`,
        type: 'search',
        description: 'Secondary search with temporal context',
        query: enhancedQuery.temporal,
        status: 'pending',
        priority: 8,
        attempts: 0,
        maxAttempts: 3,
        searchTerms: enhancedQuery.recentTerms,
        expectedInfo: 'Recent developments and current information'
      },
      {
        id: `analyze_${baseId}`,
        type: 'analyze',
        description: 'Analyze and synthesize gathered information',
        query: 'synthesize findings',
        status: 'pending',
        priority: 5,
        attempts: 0,
        maxAttempts: 1,
        dependencies: [`search_${baseId}_1`, `search_${baseId}_2`],
        expectedInfo: 'Comprehensive summary of findings'
      }
    ];
  }

  /**
   * Execute the browsing plan step by step
   */
  private async executePlan(
    plan: BrowsingPlan,
    browsingConfig: BrowsingConfig,
    progressCallback?: BrowsingProgressCallback
  ): Promise<{ success: boolean; content?: string; error?: string }> {
    plan.status = 'executing';
    plan.updatedAt = new Date().toISOString();

    console.log(`[Smart Browsing] 🚀 Starting plan execution`);

    try {
      // Execute subtasks in priority order, respecting dependencies
      while (this.hasRemainingTasks(plan)) {
        const nextTask = this.getNextExecutableTask(plan);

        if (!nextTask) {
          console.log(`[Smart Browsing] ⏸️ No executable tasks remaining, checking if plan is complete`);
          break;
        }

        console.log(`[Smart Browsing] 🔄 Executing subtask: ${nextTask.description}`);
        plan.currentSubtask = nextTask.id;
        nextTask.status = 'in_progress';

        // Enhance the subtask with context-aware query building
        const enhancedTask = this.enhanceSubtaskQuery(nextTask, plan);

        // Update the task in the plan with enhanced version
        const taskIndex = plan.subtasks.findIndex(task => task.id === nextTask.id);
        if (taskIndex !== -1) {
          plan.subtasks[taskIndex] = enhancedTask;
        }

        // Notify task started
        progressCallback?.onTaskStarted?.(enhancedTask, plan);
        progressCallback?.onStatusUpdate?.(`Executing: ${enhancedTask.description}`, plan);

        const taskResult = await this.executeSubtask(enhancedTask, plan, browsingConfig);

        if (taskResult.success) {
          nextTask.status = 'completed';
          nextTask.result = taskResult.data;
          plan.completedSubtasks.push(nextTask.id);

          // Store result in plan's gathered data for context passing
          plan.gatheredData[nextTask.id] = {
            taskType: nextTask.type,
            description: nextTask.description,
            query: nextTask.query,
            result: taskResult.data,
            completedAt: new Date().toISOString()
          };

          console.log(`[Smart Browsing] ✅ Subtask completed: ${nextTask.description}`);
          console.log(`[Smart Browsing] 📊 Stored context data for future subtasks`);

          // Notify task completed
          progressCallback?.onTaskCompleted?.(nextTask, plan);
        } else {
          nextTask.attempts++;
          nextTask.error = taskResult.error;

          if (nextTask.attempts >= nextTask.maxAttempts) {
            nextTask.status = 'failed';
            plan.failedSubtasks.push(nextTask.id);
            console.log(`[Smart Browsing] ❌ Subtask failed permanently: ${nextTask.description}`);

            // Notify task failed
            progressCallback?.onTaskFailed?.(nextTask, plan);
          } else {
            nextTask.status = 'pending';
            console.log(`[Smart Browsing] 🔄 Subtask failed, will retry (${nextTask.attempts}/${nextTask.maxAttempts}): ${nextTask.description}`);
          }
        }

        // Update progress
        plan.progress = this.calculateProgress(plan);
        progressCallback?.onProgressUpdate?.(plan.progress, plan);
        plan.updatedAt = new Date().toISOString();

        this.logProgress(plan);
      }

      // Generate final result
      const finalResult = await this.synthesizeFinalResult(plan, browsingConfig);
      plan.finalResult = finalResult;
      plan.status = 'completed';
      plan.progress = 100;

      // Notify plan completed
      progressCallback?.onPlanCompleted?.(plan);
      progressCallback?.onStatusUpdate?.('Browsing completed successfully!', plan);

      return {
        success: true,
        content: finalResult
      };

    } catch (error) {
      plan.status = 'failed';
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('[Smart Browsing] Plan execution failed:', errorMessage);

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Check if there are remaining tasks to execute
   */
  private hasRemainingTasks(plan: BrowsingPlan): boolean {
    return plan.subtasks.some(task =>
      task.status === 'pending' || task.status === 'in_progress'
    );
  }

  /**
   * Get the next executable task (highest priority, dependencies met)
   */
  private getNextExecutableTask(plan: BrowsingPlan): BrowsingSubtask | null {
    const executableTasks = plan.subtasks.filter(task => {
      if (task.status !== 'pending') return false;

      // Check if dependencies are met
      if (task.dependencies && task.dependencies.length > 0) {
        return task.dependencies.every(depId =>
          plan.completedSubtasks.includes(depId)
        );
      }

      return true;
    });

    // Sort by priority (highest first)
    executableTasks.sort((a, b) => b.priority - a.priority);

    return executableTasks[0] || null;
  }

  /**
   * Execute a single subtask
   */
  private async executeSubtask(
    subtask: BrowsingSubtask,
    plan: BrowsingPlan,
    browsingConfig: BrowsingConfig
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      switch (subtask.type) {
        case 'search':
          return await this.executeSearchSubtask(subtask, plan);

        case 'navigate':
          return await this.executeNavigateSubtask(subtask, plan);

        case 'extract':
          return await this.executeExtractSubtask(subtask, plan);

        case 'analyze':
          return await this.executeAnalyzeSubtask(subtask, plan, browsingConfig);

        default:
          throw new Error(`Unknown subtask type: ${subtask.type}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Execute a search subtask with advanced BrowserQL capabilities and smart query refinement
   */
  private async executeSearchSubtask(
    subtask: BrowsingSubtask,
    plan: BrowsingPlan
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    // Build context-aware query using results from previous subtasks
    const contextualQuery = this.buildContextualQuery(subtask, plan);
    const searchTerms = [contextualQuery, subtask.query, ...(subtask.searchTerms || [])];
    let lastError = '';
    let bestResults: any = null;
    let bestScore = 0;

    console.log(`[Smart Browsing] 🔍 Advanced search execution for: "${contextualQuery}"`);
    console.log(`[Smart Browsing] 🧠 Using context from ${plan.completedSubtasks.length} completed subtasks`);

    // Check if this requires complex automation (flight booking, form filling, etc.)
    const requiresComplexAutomation = this.detectComplexAutomationNeeds(subtask, contextualQuery);

    if (requiresComplexAutomation) {
      console.log(`[Smart Browsing] 🤖 Detected complex automation needs for: ${contextualQuery}`);
      return await this.executeComplexAutomationSearch(subtask, plan, contextualQuery);
    }

    // Try different search terms with enhanced result evaluation
    for (const searchTerm of searchTerms) {
      try {
        console.log(`[Smart Browsing] 🔍 Searching with enhanced parsing: "${searchTerm}"`);
        plan.searchQueries.push(searchTerm);

        // Use the enhanced search with better snippet extraction
        const result = await this.browserlessService.searchAndExtractUnblocked(searchTerm);

        if (result.data && result.data.results && result.data.results.length > 0) {
          console.log(`[Smart Browsing] ✅ Found ${result.data.results.length} results with enhanced snippets for: "${searchTerm}"`);

          // Calculate result quality score based on snippet content and relevance
          const resultScore = this.calculateResultQuality(result.data.results, subtask.query);

          console.log(`[Smart Browsing] 📊 Result quality score: ${resultScore.toFixed(2)} for "${searchTerm}"`);

          // Store URLs we've found
          result.data.results.forEach((item: any) => {
            if (item.link && !plan.visitedUrls.includes(item.link)) {
              plan.visitedUrls.push(item.link);
            }
          });

          // Keep track of best results
          if (resultScore > bestScore) {
            bestScore = resultScore;
            bestResults = result.data;
          }

          // If we found high-quality results, use them immediately
          if (resultScore > 0.8) {
            console.log(`[Smart Browsing] 🎯 High-quality results found (score: ${resultScore.toFixed(2)}), using immediately`);
            return {
              success: true,
              data: result.data
            };
          }

        } else {
          lastError = `No results found for: "${searchTerm}"`;
          console.log(`[Smart Browsing] ⚠️ ${lastError}`);
        }
      } catch (error) {
        lastError = error instanceof Error ? error.message : 'Search failed';
        console.log(`[Smart Browsing] ❌ Search error for "${searchTerm}": ${lastError}`);
      }
    }

    // Return best results if we found any
    if (bestResults && bestScore > 0.3) {
      console.log(`[Smart Browsing] ✅ Using best results with score: ${bestScore.toFixed(2)}`);
      return {
        success: true,
        data: bestResults
      };
    }

    return {
      success: false,
      error: `All search terms failed to find quality results. Last error: ${lastError}`
    };
  }

  /**
   * Calculate result quality score based on snippet content and relevance
   */
  private calculateResultQuality(results: any[], originalQuery: string): number {
    if (!results || results.length === 0) return 0;

    let totalScore = 0;
    const queryWords = originalQuery.toLowerCase().split(/\s+/).filter(word => word.length > 2);
    const currentYear = new Date().getFullYear().toString();

    for (const result of results) {
      let score = 0;
      const title = (result.title || '').toLowerCase();
      const snippet = (result.snippet || '').toLowerCase();
      const combinedText = `${title} ${snippet}`;

      // Base score for having content
      if (snippet && snippet !== 'no description available') {
        score += 0.3;
      }

      // Relevance score based on query word matches
      const matchedWords = queryWords.filter(word => combinedText.includes(word));
      score += (matchedWords.length / queryWords.length) * 0.4;

      // Bonus for current year (indicates recent/current information)
      if (combinedText.includes(currentYear)) {
        score += 0.2;
      }

      // Bonus for temporal keywords indicating current information
      const temporalKeywords = ['latest', 'recent', 'current', 'new', '2025', 'updated'];
      const temporalMatches = temporalKeywords.filter(keyword => combinedText.includes(keyword));
      score += Math.min(temporalMatches.length * 0.1, 0.3);

      // Penalty for very short snippets
      if (snippet.length < 50) {
        score *= 0.7;
      }

      // Bonus for longer, more informative snippets
      if (snippet.length > 150) {
        score += 0.1;
      }

      totalScore += score;
    }

    return Math.min(totalScore / results.length, 1.0);
  }

  /**
   * Execute navigate subtask with advanced automation capabilities
   */
  private async executeNavigateSubtask(
    subtask: BrowsingSubtask,
    plan: BrowsingPlan
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const query = subtask.query;
      console.log(`[Smart Browsing] 🌐 Processing advanced navigation task: ${query}`);

      // Check if the query is a valid URL
      const urlPattern = /^https?:\/\//i;
      if (urlPattern.test(query)) {
        // Direct URL navigation with automation detection
        console.log(`[Smart Browsing] 🌐 Navigating to URL with automation support: ${query}`);

        if (!plan.visitedUrls.includes(query)) {
          plan.visitedUrls.push(query);
        }

        // Check if this requires complex automation
        const requiresComplexAutomation = this.detectComplexAutomationNeeds(subtask, query);

        if (requiresComplexAutomation) {
          console.log(`[Smart Browsing] 🤖 Using complex automation workflow for: ${query}`);
          return await this.executeComplexAutomation(subtask, query, plan);
        } else {
          // Standard navigation with enhanced error handling
          const result = await this.browserlessService.navigateAndExtract(query);

          if (result.data) {
            console.log(`[Smart Browsing] ✅ Navigation successful for: ${query}`);
            return {
              success: true,
              data: result.data
            };
          } else {
            return {
              success: false,
              error: 'No data extracted from navigation'
            };
          }
        }
      } else {
        // Not a direct URL - convert to search task with enhanced query processing
        console.log(`[Smart Browsing] 🔄 Converting navigation to enhanced search: ${query}`);

        // Extract meaningful search terms from the navigation description
        let searchQuery = query;
        if (query.toLowerCase().includes('navigate to')) {
          searchQuery = query.replace(/navigate to\s*/i, '').trim();
        }
        if (query.toLowerCase().includes('websites of')) {
          searchQuery = searchQuery.replace(/websites of\s*/i, '').trim();
        }

        // Add current year for better results
        const currentYear = new Date().getFullYear();
        if (!searchQuery.includes(currentYear.toString())) {
          searchQuery = `${searchQuery} ${currentYear}`;
        }

        // Use the enhanced search functionality
        return await this.executeSearchSubtask({
          ...subtask,
          type: 'search',
          query: searchQuery,
          searchTerms: [
            searchQuery,
            `${searchQuery} latest`,
            `${searchQuery} official website`,
            `${searchQuery} information`
          ]
        }, plan);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Navigation failed';
      console.log(`[Smart Browsing] ❌ Navigation error: ${errorMessage}`);
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Detect if a subtask requires complex automation (forms, CAPTCHAs, etc.)
   */
  private detectComplexAutomationNeeds(subtask: BrowsingSubtask, url: string): boolean {
    const description = (subtask.description || '').toLowerCase();
    const query = (subtask.query || '').toLowerCase();

    // Keywords that indicate complex automation needs
    const complexKeywords = [
      'form', 'submit', 'login', 'register', 'book', 'reserve', 'purchase',
      'checkout', 'payment', 'captcha', 'verify', 'authenticate', 'sign in',
      'sign up', 'create account', 'fill out', 'application', 'survey',
      'reservation', 'booking', 'order'
    ];

    // Flight booking specific keywords
    const flightBookingKeywords = [
      'earliest flight', 'flight from', 'flight to', 'book flight', 'search flight',
      'flight booking', 'airline', 'departure', 'arrival', 'connecting flight',
      'flight schedule', 'flight search', 'travel booking'
    ];

    // URL patterns that often require complex automation
    const complexUrlPatterns = [
      'login', 'register', 'checkout', 'booking', 'reservation', 'form',
      'application', 'survey', 'account', 'dashboard', 'admin', 'portal'
    ];

    // Check for flight booking scenarios
    const isFlightBooking = flightBookingKeywords.some(keyword =>
      description.includes(keyword) || query.includes(keyword)
    );

    // Check for general complex automation needs
    const isComplexAutomation = complexKeywords.some(keyword =>
      description.includes(keyword) || query.includes(keyword)
    ) || complexUrlPatterns.some(pattern => url.toLowerCase().includes(pattern));

    // Flight booking always requires complex automation for form filling
    return isFlightBooking || isComplexAutomation;
  }

  /**
   * Execute complex automation workflow using advanced BrowserQL capabilities
   */
  private async executeComplexAutomation(
    subtask: BrowsingSubtask,
    url: string,
    plan: BrowsingPlan
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.log(`[Smart Browsing] 🤖 Starting complex automation for: ${url}`);

      // Create a workflow based on the subtask requirements
      const workflow = this.buildAutomationWorkflow(subtask, url);

      const result = await this.browserlessService.executeComplexWorkflow(url, workflow);

      if (result && result.data) {
        console.log(`[Smart Browsing] ✅ Complex automation successful for: ${url}`);

        // Store session info for potential follow-up tasks
        if (result.sessionId && result.reconnectUrl) {
          (plan as any).sessionInfo = {
            sessionId: result.sessionId,
            reconnectUrl: result.reconnectUrl,
            lastUsed: new Date()
          };
        }

        return {
          success: true,
          data: result.data
        };
      } else {
        return {
          success: false,
          error: 'Complex automation completed but no data extracted'
        };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Complex automation failed';
      console.log(`[Smart Browsing] ❌ Complex automation error: ${errorMessage}`);
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Build automation workflow steps based on subtask requirements
   */
  private buildAutomationWorkflow(subtask: BrowsingSubtask, url: string): any[] {
    const workflow = [];
    const description = (subtask.description || '').toLowerCase();
    const query = (subtask.query || '').toLowerCase();

    // Always start with navigation
    workflow.push({
      name: 'navigate',
      type: 'navigate',
      params: { url, waitUntil: 'networkIdle' }
    });

    // Add wait for page to stabilize
    workflow.push({
      name: 'wait_for_page',
      type: 'wait',
      params: { time: 3000 }
    });

    // Handle cookie consent/popups that might block interaction
    workflow.push({
      name: 'handle_cookie_consent',
      type: 'click',
      params: {
        selector: 'button[id*="accept"], button[class*="accept"], button[class*="consent"], .cookie-accept, #cookie-accept, [data-testid*="accept"]',
        timeout: 5000,
        optional: true
      }
    });

    // Wait after handling popups
    workflow.push({
      name: 'wait_after_popups',
      type: 'wait',
      params: { time: 2000 }
    });

    // Handle CAPTCHA if likely to be present
    if (description.includes('captcha') || description.includes('verify') ||
        description.includes('challenge') || description.includes('protection')) {
      workflow.push({
        name: 'solve_captcha',
        type: 'captcha',
        params: { type: 'hcaptcha' } // Default to hCaptcha, can detect others
      });
    }

    // Handle search functionality
    if (description.includes('search') || query.includes('search') ||
        description.includes('find') || description.includes('look for')) {
      workflow.push({
        name: 'find_search_input',
        type: 'waitForSelector',
        params: {
          selector: 'input[type="search"], input[name*="search"], input[id*="search"], input[placeholder*="search"], .search-input',
          timeout: 10000
        }
      });

      // Extract search terms from the query
      const searchTerms = this.extractSearchTermsFromQuery(query);
      if (searchTerms) {
        workflow.push({
          name: 'enter_search_terms',
          type: 'type',
          params: {
            selector: 'input[type="search"], input[name*="search"], input[id*="search"], input[placeholder*="search"], .search-input',
            text: searchTerms
          }
        });

        workflow.push({
          name: 'submit_search',
          type: 'click',
          params: {
            selector: 'button[type="submit"], button[class*="search"], .search-button, input[type="submit"]'
          }
        });

        workflow.push({
          name: 'wait_for_search_results',
          type: 'wait',
          params: { time: 3000 }
        });
      }
    }

    // Handle form filling
    if (description.includes('form') || description.includes('fill') ||
        description.includes('submit') || description.includes('input')) {
      workflow.push({
        name: 'wait_for_form',
        type: 'waitForSelector',
        params: {
          selector: 'form, input, textarea, select',
          timeout: 10000
        }
      });

      // Extract form structure for analysis
      workflow.push({
        name: 'analyze_form',
        type: 'extract',
        params: {
          selector: 'form, input, textarea, select',
          type: 'html'
        }
      });
    }

    // Handle login/authentication
    if (description.includes('login') || description.includes('sign in') ||
        description.includes('authenticate') || description.includes('credentials')) {
      workflow.push({
        name: 'wait_for_login_form',
        type: 'waitForSelector',
        params: {
          selector: 'form[action*="login"], form[id*="login"], input[type="password"], .login-form',
          timeout: 10000
        }
      });

      workflow.push({
        name: 'extract_login_form',
        type: 'extract',
        params: {
          selector: 'form[action*="login"], form[id*="login"], input[type="password"], .login-form',
          type: 'html'
        }
      });
    }

    // Handle shopping/product pages
    if (description.includes('product') || description.includes('buy') ||
        description.includes('purchase') || description.includes('shop')) {
      workflow.push({
        name: 'extract_product_info',
        type: 'extract',
        params: {
          selector: '.product, .item, [data-testid*="product"], .price, .title, .description',
          type: 'html'
        }
      });
    }

    // Handle infinite scroll and pagination
    if (description.includes('scroll') || description.includes('load more') ||
        description.includes('pagination') || description.includes('infinite') ||
        description.includes('more results')) {

      // First scroll
      workflow.push({
        name: 'scroll_content',
        type: 'scroll',
        params: { selector: 'body', distance: 1000 }
      });

      workflow.push({
        name: 'wait_after_scroll',
        type: 'wait',
        params: { time: 2000 }
      });

      // Second scroll for more content
      workflow.push({
        name: 'scroll_more',
        type: 'scroll',
        params: { selector: 'body', distance: 1000 }
      });

      workflow.push({
        name: 'wait_after_second_scroll',
        type: 'wait',
        params: { time: 2000 }
      });

      // Try to click "Load More" button if present
      workflow.push({
        name: 'click_load_more',
        type: 'click',
        params: {
          selector: 'button[class*="load"], button[class*="more"], .load-more, .show-more, [data-testid*="load"]',
          timeout: 5000,
          optional: true
        }
      });
    }

    // Handle booking/reservation specific workflows
    if (description.includes('book') || description.includes('reserve') ||
        description.includes('appointment') || description.includes('schedule')) {
      workflow.push({
        name: 'wait_for_booking_interface',
        type: 'waitForSelector',
        params: {
          selector: '.booking, .reservation, .calendar, .schedule, [class*="book"], [class*="reserve"]',
          timeout: 10000
        }
      });

      workflow.push({
        name: 'extract_booking_options',
        type: 'extract',
        params: {
          selector: '.booking, .reservation, .calendar, .schedule, [class*="book"], [class*="reserve"]',
          type: 'html'
        }
      });
    }

    // Always end with comprehensive content extraction
    workflow.push({
      name: 'extract_main_content',
      type: 'extract',
      params: {
        selector: 'main, .main, .content, .container, body',
        type: 'text'
      }
    });

    // Extract structured data if available
    workflow.push({
      name: 'extract_structured_data',
      type: 'extract',
      params: {
        selector: '[itemscope], [data-*], .product, .article, .post, .result, .listing',
        type: 'html'
      }
    });

    // Extract navigation and links
    workflow.push({
      name: 'extract_navigation',
      type: 'extract',
      params: {
        selector: 'nav, .nav, .navigation, .menu, a[href]',
        type: 'html'
      }
    });

    // Take screenshot for debugging/verification
    workflow.push({
      name: 'take_screenshot',
      type: 'screenshot',
      params: { fullPage: false }
    });

    console.log(`[Smart Browsing] 🔧 Built automation workflow with ${workflow.length} steps for: ${url}`);
    return workflow;
  }

  /**
   * Extract search terms from a query string
   */
  private extractSearchTermsFromQuery(query: string): string | null {
    // Remove common browsing instruction words and extract the actual search terms
    const cleanQuery = query
      .replace(/^(search for|find|look for|search|browse|navigate to)\s+/i, '')
      .replace(/\s+(on|in|at|from)\s+\w+\.(com|org|net|io).*$/i, '')
      .trim();

    return cleanQuery.length > 0 ? cleanQuery : null;
  }

  /**
   * Build specialized workflow for flight booking automation
   */
  private buildFlightBookingWorkflow(subtask: BrowsingSubtask, plan: BrowsingPlan, site: string): any[] {
    const workflow = [];
    const description = subtask.description.toLowerCase();

    // Get context from previous subtasks
    const context = this.getFlightBookingContext(plan);

    console.log(`[Smart Browsing] 🛫 Building flight booking workflow for: ${site}`);
    console.log(`[Smart Browsing] 📋 Context:`, context);

    // Navigate to the booking site
    workflow.push({
      name: 'navigate_to_booking_site',
      type: 'navigate',
      params: { url: site, waitUntil: 'networkIdle' }
    });

    // Wait for page to load
    workflow.push({
      name: 'wait_for_page_load',
      type: 'wait',
      params: { time: 3000 }
    });

    // Handle cookie banners and popups
    workflow.push({
      name: 'dismiss_popups',
      type: 'click',
      params: {
        selector: '[data-testid="cookie-banner-close"], .cookie-banner button, .modal-close, [aria-label="Close"]',
        optional: true
      }
    });

    // Fill origin airport with site-specific selectors
    if (context.origin) {
      const originSelectors = this.getOriginSelectors(site);

      workflow.push({
        name: 'fill_origin',
        type: 'type',
        params: {
          selector: originSelectors.join(', '),
          text: context.origin,
          clear: true
        }
      });

      // Wait for autocomplete dropdown
      workflow.push({
        name: 'wait_for_origin_autocomplete',
        type: 'wait',
        params: { time: 1500 }
      });

      // Select first autocomplete option
      workflow.push({
        name: 'select_origin',
        type: 'click',
        params: {
          selector: '.autocomplete-item:first-child, .suggestion:first-child, li:first-child, [role="option"]:first-child',
          optional: true
        }
      });
    }

    // Fill destination airport with site-specific selectors
    if (context.destination) {
      const destinationSelectors = this.getDestinationSelectors(site);

      workflow.push({
        name: 'fill_destination',
        type: 'type',
        params: {
          selector: destinationSelectors.join(', '),
          text: context.destination,
          clear: true
        }
      });

      // Wait for autocomplete dropdown
      workflow.push({
        name: 'wait_for_destination_autocomplete',
        type: 'wait',
        params: { time: 1500 }
      });

      // Select first autocomplete option
      workflow.push({
        name: 'select_destination',
        type: 'click',
        params: {
          selector: '.autocomplete-item:first-child, .suggestion:first-child, li:first-child, [role="option"]:first-child',
          optional: true
        }
      });
    }

    // Set departure date (today) with site-specific selectors
    const dateSelectors = this.getDateSelectors(site);

    workflow.push({
      name: 'click_departure_date',
      type: 'click',
      params: {
        selector: dateSelectors.join(', ')
      }
    });

    // Wait for date picker to open
    workflow.push({
      name: 'wait_for_date_picker',
      type: 'wait',
      params: { time: 1000 }
    });

    // Select today's date with multiple selector strategies
    const today = new Date();
    const todayFormatted = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    const todaySelectors = [
      `[data-date="${todayFormatted}"]`,
      `[aria-label*="${today.getDate()}"]`,
      '.today',
      '.current-day',
      '.selected',
      `button:contains("${today.getDate()}")`,
      `td:contains("${today.getDate()}")`
    ];

    workflow.push({
      name: 'select_today',
      type: 'click',
      params: {
        selector: todaySelectors.join(', '),
        optional: true
      }
    });

    // Set one-way trip if this is for connecting flights
    if (description.includes('connecting') || description.includes('after arrival')) {
      workflow.push({
        name: 'select_one_way',
        type: 'click',
        params: {
          selector: 'input[value="oneway"], label[for*="oneway"], .trip-type .one-way, [data-testid*="oneway"]',
          optional: true
        }
      });
    }

    // Wait before submitting to ensure all fields are filled
    workflow.push({
      name: 'wait_before_submit',
      type: 'wait',
      params: { time: 2000 }
    });

    // Submit search with site-specific selectors
    const searchButtonSelectors = this.getSearchButtonSelectors(site);

    workflow.push({
      name: 'submit_search',
      type: 'click',
      params: {
        selector: searchButtonSelectors.join(', ')
      }
    });

    // Wait for results to load
    workflow.push({
      name: 'wait_for_results',
      type: 'wait',
      params: { time: 5000 }
    });

    // Handle loading states
    workflow.push({
      name: 'wait_for_loading_complete',
      type: 'waitForSelector',
      params: {
        selector: '.flight-results, .search-results, [data-testid="results"]',
        timeout: 15000
      }
    });

    // Extract flight information
    workflow.push({
      name: 'extract_flight_results',
      type: 'extract',
      params: {
        selector: '.flight-result, .flight-option, .search-result, [data-testid*="flight"]',
        type: 'html'
      }
    });

    // Extract specific flight times and details
    workflow.push({
      name: 'extract_flight_details',
      type: 'extract',
      params: {
        selector: '.departure-time, .arrival-time, .flight-duration, .airline-name, .price',
        type: 'text'
      }
    });

    // Take screenshot for verification
    workflow.push({
      name: 'take_results_screenshot',
      type: 'screenshot',
      params: { fullPage: false }
    });

    console.log(`[Smart Browsing] 🛫 Built flight booking workflow with ${workflow.length} steps`);
    return workflow;
  }

  /**
   * Get flight booking context from completed subtasks
   */
  private getFlightBookingContext(plan: BrowsingPlan): any {
    const context: any = {
      origin: null,
      destination: null,
      departureTime: null,
      arrivalTime: null,
      duration: null
    };

    // Extract context from the original query and plan goal
    const queryLower = plan.originalQuery.toLowerCase();
    const goalLower = plan.goal.toLowerCase();

    // Extract origin and destination from query
    if (queryLower.includes('owerri') && queryLower.includes('abuja')) {
      context.origin = 'Owerri';
      context.destination = 'Abuja';
    } else if (queryLower.includes('abuja') && queryLower.includes('dubai')) {
      context.origin = 'Abuja';
      context.destination = 'Dubai';
    }

    // Extract any timing information from completed subtasks
    const completedSubtasks = plan.subtasks.filter(task => task.status === 'completed' && task.result);

    for (const task of completedSubtasks) {
      const result = JSON.stringify(task.result).toLowerCase();

      // Look for departure times
      const timeMatch = result.match(/(\d{1,2}:\d{2}(?:\s*(?:am|pm))?)/);
      if (timeMatch && !context.departureTime) {
        context.departureTime = timeMatch[1];
      }

      // Look for duration
      const durationMatch = result.match(/(\d+(?:\s*(?:hours?|hrs?|h))?(?:\s*(?:and|\&)?\s*\d+(?:\s*(?:minutes?|mins?|m))?)?)/);
      if (durationMatch && !context.duration) {
        context.duration = durationMatch[1];
      }
    }

    return context;
  }

  /**
   * Get site-specific origin selectors
   */
  private getOriginSelectors(site: string): string[] {
    const siteHost = new URL(site).hostname.toLowerCase();

    const commonSelectors = [
      'input[placeholder*="From"]',
      'input[name*="origin"]',
      'input[id*="from"]',
      'input[aria-label*="From"]',
      'input[data-testid*="origin"]',
      'input[data-testid*="from"]'
    ];

    if (siteHost.includes('kayak')) {
      return [
        'input[placeholder*="From"]',
        'input[aria-label*="Flight origin"]',
        'input[data-testid="origin"]',
        ...commonSelectors
      ];
    } else if (siteHost.includes('expedia')) {
      return [
        'input[id*="flight-origin"]',
        'input[aria-label*="Leaving from"]',
        'input[data-testid*="origin"]',
        ...commonSelectors
      ];
    } else if (siteHost.includes('skyscanner')) {
      return [
        'input[placeholder*="From"]',
        'input[data-testid*="origin"]',
        'input[name*="OriginPlace"]',
        ...commonSelectors
      ];
    } else if (siteHost.includes('google')) {
      return [
        'input[placeholder*="Where from"]',
        'input[aria-label*="Where from"]',
        'input[jsname*="origin"]',
        ...commonSelectors
      ];
    }

    return commonSelectors;
  }

  /**
   * Get site-specific destination selectors
   */
  private getDestinationSelectors(site: string): string[] {
    const siteHost = new URL(site).hostname.toLowerCase();

    const commonSelectors = [
      'input[placeholder*="To"]',
      'input[name*="destination"]',
      'input[id*="to"]',
      'input[aria-label*="To"]',
      'input[data-testid*="destination"]',
      'input[data-testid*="to"]'
    ];

    if (siteHost.includes('kayak')) {
      return [
        'input[placeholder*="To"]',
        'input[aria-label*="Flight destination"]',
        'input[data-testid="destination"]',
        ...commonSelectors
      ];
    } else if (siteHost.includes('expedia')) {
      return [
        'input[id*="flight-destination"]',
        'input[aria-label*="Going to"]',
        'input[data-testid*="destination"]',
        ...commonSelectors
      ];
    } else if (siteHost.includes('skyscanner')) {
      return [
        'input[placeholder*="To"]',
        'input[data-testid*="destination"]',
        'input[name*="DestinationPlace"]',
        ...commonSelectors
      ];
    } else if (siteHost.includes('google')) {
      return [
        'input[placeholder*="Where to"]',
        'input[aria-label*="Where to"]',
        'input[jsname*="destination"]',
        ...commonSelectors
      ];
    }

    return commonSelectors;
  }

  /**
   * Get site-specific date selectors
   */
  private getDateSelectors(site: string): string[] {
    const siteHost = new URL(site).hostname.toLowerCase();

    const commonSelectors = [
      'input[placeholder*="Departure"]',
      'input[name*="departure"]',
      'input[id*="depart"]',
      'input[data-testid*="departure"]',
      'button[data-testid*="departure"]'
    ];

    if (siteHost.includes('kayak')) {
      return [
        'input[aria-label*="Start date"]',
        'input[data-testid="departure-date"]',
        'button[data-testid="departure-date"]',
        ...commonSelectors
      ];
    } else if (siteHost.includes('expedia')) {
      return [
        'input[id*="flight-departing"]',
        'button[data-testid*="departure"]',
        'input[aria-label*="Departing"]',
        ...commonSelectors
      ];
    } else if (siteHost.includes('skyscanner')) {
      return [
        'input[placeholder*="Depart"]',
        'button[data-testid*="depart"]',
        'input[name*="OutboundDate"]',
        ...commonSelectors
      ];
    } else if (siteHost.includes('google')) {
      return [
        'input[placeholder*="Departure"]',
        'input[aria-label*="Departure"]',
        'div[data-value*="departure"]',
        ...commonSelectors
      ];
    }

    return commonSelectors;
  }

  /**
   * Get site-specific search button selectors
   */
  private getSearchButtonSelectors(site: string): string[] {
    const siteHost = new URL(site).hostname.toLowerCase();

    const commonSelectors = [
      'button[type="submit"]',
      '.search-button',
      'button:contains("Search")',
      '[data-testid="submit"]',
      'button[aria-label*="Search"]'
    ];

    if (siteHost.includes('kayak')) {
      return [
        'button[aria-label*="Search"]',
        'button[data-testid="submit-button"]',
        '.Common-Widgets-Button-ButtonPrimary',
        ...commonSelectors
      ];
    } else if (siteHost.includes('expedia')) {
      return [
        'button[data-testid*="search"]',
        'button[aria-label*="Search"]',
        '.btn-primary',
        ...commonSelectors
      ];
    } else if (siteHost.includes('skyscanner')) {
      return [
        'button[data-testid*="search"]',
        'button:contains("Search flights")',
        '.BpkButton--primary',
        ...commonSelectors
      ];
    } else if (siteHost.includes('google')) {
      return [
        'button[aria-label*="Search"]',
        'button[jsname*="search"]',
        '.VfPpkd-LgbsSe',
        ...commonSelectors
      ];
    }

    return commonSelectors;
  }

  /**
   * Execute extract subtask
   */
  private async executeExtractSubtask(
    subtask: BrowsingSubtask,
    plan: BrowsingPlan
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    // Similar to navigate but with specific extraction focus
    return this.executeNavigateSubtask(subtask, plan);
  }

  /**
   * Execute analyze subtask - synthesize gathered information
   */
  private async executeAnalyzeSubtask(
    subtask: BrowsingSubtask,
    plan: BrowsingPlan,
    browsingConfig: BrowsingConfig
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.log(`[Smart Browsing] 🧠 Analyzing gathered data...`);

      // Collect all data from completed subtasks
      const gatheredData = plan.subtasks
        .filter(task => task.status === 'completed' && task.result)
        .map(task => ({
          type: task.type,
          description: task.description,
          query: task.query,
          data: task.result
        }));

      if (gatheredData.length === 0) {
        return {
          success: false,
          error: 'No data available for analysis'
        };
      }

      // Use AI to analyze and synthesize the data
      const model = browsingConfig.browsing_models[0];
      if (!model) {
        return {
          success: false,
          error: 'No AI model available for analysis'
        };
      }

      const analysisPrompt = this.buildAnalysisPrompt(plan.originalQuery, gatheredData);
      const aiResult = await this.callAIForAnalysis(analysisPrompt, model);

      if (aiResult.success && aiResult.content) {
        return {
          success: true,
          data: {
            analysis: aiResult.content,
            sourceData: gatheredData
          }
        };
      } else {
        return {
          success: false,
          error: aiResult.error || 'Analysis failed'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Analysis failed'
      };
    }
  }

  /**
   * Calculate progress percentage
   */
  private calculateProgress(plan: BrowsingPlan): number {
    const totalTasks = plan.subtasks.length;
    const completedTasks = plan.completedSubtasks.length;
    return Math.round((completedTasks / totalTasks) * 100);
  }

  /**
   * Log progress update
   */
  private logProgress(plan: BrowsingPlan): void {
    const completed = plan.completedSubtasks.length;
    const failed = plan.failedSubtasks.length;
    const total = plan.subtasks.length;
    const remaining = total - completed - failed;

    console.log(`[Smart Browsing] 📊 Progress: ${plan.progress}% (${completed}/${total} completed, ${failed} failed, ${remaining} remaining)`);
  }

  /**
   * Build context-aware query using results from previous subtasks
   */
  private buildContextualQuery(subtask: BrowsingSubtask, plan: BrowsingPlan): string {
    let contextualQuery = subtask.query;

    // Get context from completed subtasks
    const completedSubtasks = plan.subtasks.filter(task =>
      task.status === 'completed' && task.result && task.id !== subtask.id
    );

    if (completedSubtasks.length === 0) {
      return contextualQuery;
    }

    console.log(`[Smart Browsing] 🧠 Building contextual query from ${completedSubtasks.length} completed subtasks`);

    // Extract key information from previous results
    const contextInfo: string[] = [];

    for (const completedTask of completedSubtasks) {
      const result = completedTask.result;

      // Extract specific information based on task type and content
      if (completedTask.description.toLowerCase().includes('flight') &&
          completedTask.description.toLowerCase().includes('earliest')) {
        // Extract flight times, airlines, etc.
        const flightInfo = this.extractFlightInfo(result);
        if (flightInfo) {
          contextInfo.push(flightInfo);
        }
      }

      if (completedTask.description.toLowerCase().includes('duration')) {
        // Extract duration information
        const durationInfo = this.extractDurationInfo(result);
        if (durationInfo) {
          contextInfo.push(durationInfo);
        }
      }

      // Extract arrival times for connecting flights
      if (completedTask.description.toLowerCase().includes('arrive')) {
        const arrivalInfo = this.extractArrivalInfo(result);
        if (arrivalInfo) {
          contextInfo.push(arrivalInfo);
        }
      }
    }

    // Enhance the query with context
    if (contextInfo.length > 0) {
      const context = contextInfo.join(', ');

      // Build more specific contextual queries based on subtask type
      if (subtask.description.toLowerCase().includes('connecting') ||
          subtask.description.toLowerCase().includes('after arrival')) {
        // For connecting flights, calculate departure time based on arrival + buffer
        const arrivalTime = this.extractTimeFromContext(contextInfo);
        if (arrivalTime) {
          const departureTime = this.calculateConnectingFlightTime(arrivalTime);
          contextualQuery = `${subtask.query} departing after ${departureTime}`;
        } else {
          contextualQuery = `${subtask.query} departing after ${context}`;
        }
      } else if (subtask.description.toLowerCase().includes('duration') &&
                 contextInfo.some(info => info.includes('flight'))) {
        // For duration queries, be more specific about which flight
        const flightInfo = contextInfo.find(info => info.includes('flight'));
        contextualQuery = `${subtask.query} for ${flightInfo}`;
      } else if (subtask.description.toLowerCase().includes('earliest') &&
                 contextInfo.some(info => info.includes('arrives'))) {
        // For earliest flight queries after knowing arrival time
        const arrivalInfo = contextInfo.find(info => info.includes('arrives'));
        contextualQuery = `${subtask.query} after ${arrivalInfo}`;
      } else {
        contextualQuery = `${subtask.query} (context: ${context})`;
      }

      console.log(`[Smart Browsing] 🎯 Enhanced query with context: "${contextualQuery}"`);
    }

    return contextualQuery;
  }

  /**
   * Extract flight information from search results
   */
  private extractFlightInfo(result: any): string | null {
    if (!result) return null;

    try {
      const resultStr = JSON.stringify(result).toLowerCase();

      // Look for flight times (e.g., "06:30", "6:30 am", etc.)
      const timePattern = /(\d{1,2}:\d{2}(?:\s*(?:am|pm))?)/gi;
      const times = resultStr.match(timePattern);

      // Look for airlines
      const airlinePattern = /(arik air|air peace|dana air|azman air|emirates|qatar|turkish)/gi;
      const airlines = resultStr.match(airlinePattern);

      if (times && times.length > 0) {
        const earliestTime = times[0];
        const airline = airlines ? airlines[0] : '';
        return `earliest flight ${earliestTime}${airline ? ` on ${airline}` : ''}`;
      }
    } catch (error) {
      console.warn('[Smart Browsing] Error extracting flight info:', error);
    }

    return null;
  }

  /**
   * Extract duration information from search results
   */
  private extractDurationInfo(result: any): string | null {
    if (!result) return null;

    try {
      const resultStr = JSON.stringify(result).toLowerCase();

      // Look for duration patterns (e.g., "2 hours 30 minutes", "2h 30m", etc.)
      const durationPattern = /(\d+(?:\s*(?:hours?|hrs?|h))?(?:\s*(?:and|\&)?\s*\d+(?:\s*(?:minutes?|mins?|m))?)?)/gi;
      const durations = resultStr.match(durationPattern);

      if (durations && durations.length > 0) {
        return `duration ${durations[0]}`;
      }
    } catch (error) {
      console.warn('[Smart Browsing] Error extracting duration info:', error);
    }

    return null;
  }

  /**
   * Extract arrival time information from search results
   */
  private extractArrivalInfo(result: any): string | null {
    if (!result) return null;

    try {
      const resultStr = JSON.stringify(result).toLowerCase();

      // Look for arrival times
      const arrivalPattern = /(?:arrives?|arrival|landing)(?:\s*(?:at|in|by))?\s*(\d{1,2}:\d{2}(?:\s*(?:am|pm))?)/gi;
      const arrivals = resultStr.match(arrivalPattern);

      if (arrivals && arrivals.length > 0) {
        return `arrives ${arrivals[0]}`;
      }
    } catch (error) {
      console.warn('[Smart Browsing] Error extracting arrival info:', error);
    }

    return null;
  }

  /**
   * Extract time information from context
   */
  private extractTimeFromContext(contextInfo: string[]): string | null {
    for (const info of contextInfo) {
      const timeMatch = info.match(/(\d{1,2}:\d{2}(?:\s*(?:am|pm))?)/i);
      if (timeMatch) {
        return timeMatch[1];
      }
    }
    return null;
  }

  /**
   * Calculate connecting flight departure time with buffer
   */
  private calculateConnectingFlightTime(arrivalTime: string): string {
    try {
      // Parse the arrival time
      const timeMatch = arrivalTime.match(/(\d{1,2}):(\d{2})(?:\s*(am|pm))?/i);
      if (!timeMatch) return arrivalTime;

      let hours = parseInt(timeMatch[1]);
      const minutes = parseInt(timeMatch[2]);
      const ampm = timeMatch[3]?.toLowerCase();

      // Convert to 24-hour format
      if (ampm === 'pm' && hours !== 12) {
        hours += 12;
      } else if (ampm === 'am' && hours === 12) {
        hours = 0;
      }

      // Add 2-hour buffer for international connections, 1 hour for domestic
      const bufferHours = 2; // Assume international for safety
      hours += bufferHours;

      // Handle day overflow
      if (hours >= 24) {
        hours -= 24;
      }

      // Format back to readable time
      const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
      const displayAmPm = hours >= 12 ? 'PM' : 'AM';

      return `${displayHours}:${String(minutes).padStart(2, '0')} ${displayAmPm}`;
    } catch (error) {
      console.warn('[Smart Browsing] Error calculating connecting flight time:', error);
      return arrivalTime;
    }
  }

  /**
   * Enhance subtask query with dynamic context-aware modifications
   */
  private enhanceSubtaskQuery(subtask: BrowsingSubtask, plan: BrowsingPlan): BrowsingSubtask {
    const enhancedSubtask = { ...subtask };

    // Get context from completed subtasks
    const completedSubtasks = plan.subtasks.filter(task =>
      task.status === 'completed' && task.result && task.id !== subtask.id
    );

    if (completedSubtasks.length === 0) {
      return enhancedSubtask;
    }

    // Enhance search terms based on context
    const contextualTerms: string[] = [];

    for (const completedTask of completedSubtasks) {
      const result = completedTask.result;

      // Extract specific information for search term enhancement
      if (completedTask.description.toLowerCase().includes('earliest flight')) {
        const flightInfo = this.extractFlightInfo(result);
        if (flightInfo) {
          // Add specific flight details to search terms
          contextualTerms.push(`${subtask.query} ${flightInfo}`);
          contextualTerms.push(`${subtask.query} after ${flightInfo}`);
        }
      }

      if (completedTask.description.toLowerCase().includes('duration')) {
        const durationInfo = this.extractDurationInfo(result);
        if (durationInfo) {
          // Use duration info for more specific searches
          contextualTerms.push(`${subtask.query} ${durationInfo}`);
        }
      }
    }

    // Add contextual terms to search terms
    if (contextualTerms.length > 0) {
      enhancedSubtask.searchTerms = [
        ...(enhancedSubtask.searchTerms || []),
        ...contextualTerms
      ];

      console.log(`[Smart Browsing] 🎯 Enhanced subtask with ${contextualTerms.length} contextual search terms`);
    }

    return enhancedSubtask;
  }

  /**
   * Execute complex automation search for flight booking and form filling
   */
  private async executeComplexAutomationSearch(
    subtask: BrowsingSubtask,
    plan: BrowsingPlan,
    query: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    console.log(`[Smart Browsing] 🤖 Starting complex automation search for: ${query}`);

    // Determine the best sites to use based on the query type
    const targetSites = this.getTargetSitesForQuery(query, subtask);

    for (const site of targetSites) {
      try {
        console.log(`[Smart Browsing] 🌐 Attempting automation on: ${site}`);

        // Build automation workflow based on the task type
        const workflow = this.buildTaskSpecificWorkflow(subtask, plan, site, query);

        const result = await this.browserlessService.executeComplexWorkflow(site, workflow);

        if (result && result.data) {
          console.log(`[Smart Browsing] ✅ Complex automation successful on: ${site}`);
          return {
            success: true,
            data: result.data
          };
        }
      } catch (error) {
        console.log(`[Smart Browsing] ❌ Automation failed on ${site}:`, error);
        continue;
      }
    }

    return {
      success: false,
      error: 'All target sites failed automation'
    };
  }

  /**
   * Get target sites based on query type and content
   */
  private getTargetSitesForQuery(query: string, subtask: BrowsingSubtask): string[] {
    const description = (subtask.description || '').toLowerCase();
    const queryLower = query.toLowerCase();

    // Flight booking sites
    if (queryLower.includes('flight') || queryLower.includes('airline') ||
        queryLower.includes('departure') || queryLower.includes('arrival')) {
      return [
        'https://www.kayak.com',
        'https://www.expedia.com',
        'https://www.skyscanner.com',
        'https://www.google.com/flights'
      ];
    }

    // Hotel booking sites
    if (queryLower.includes('hotel') || queryLower.includes('accommodation') ||
        queryLower.includes('booking') || queryLower.includes('stay')) {
      return [
        'https://www.booking.com',
        'https://www.expedia.com',
        'https://www.hotels.com',
        'https://www.kayak.com'
      ];
    }

    // Restaurant reservation sites
    if (queryLower.includes('restaurant') || queryLower.includes('reservation') ||
        queryLower.includes('dining') || queryLower.includes('table')) {
      return [
        'https://www.opentable.com',
        'https://www.yelp.com',
        'https://www.resy.com'
      ];
    }

    // Shopping sites
    if (queryLower.includes('buy') || queryLower.includes('purchase') ||
        queryLower.includes('shop') || queryLower.includes('product')) {
      return [
        'https://www.amazon.com',
        'https://www.google.com/shopping',
        'https://www.ebay.com'
      ];
    }

    // Job search sites
    if (queryLower.includes('job') || queryLower.includes('career') ||
        queryLower.includes('employment') || queryLower.includes('hiring')) {
      return [
        'https://www.linkedin.com/jobs',
        'https://www.indeed.com',
        'https://www.glassdoor.com'
      ];
    }

    // Default to general search engines for complex automation
    return [
      'https://www.google.com',
      'https://www.bing.com',
      'https://duckduckgo.com'
    ];
  }

  /**
   * Build task-specific workflow based on the query and site
   */
  private buildTaskSpecificWorkflow(
    subtask: BrowsingSubtask,
    plan: BrowsingPlan,
    site: string,
    query: string
  ): any[] {
    const queryLower = query.toLowerCase();

    // Use flight booking workflow for flight-related queries
    if (queryLower.includes('flight') || queryLower.includes('airline')) {
      return this.buildFlightBookingWorkflow(subtask, plan, site);
    }

    // Use general automation workflow for other tasks
    return this.buildAutomationWorkflow(subtask, site);
  }

  /**
   * Build analysis prompt for AI
   */
  private buildAnalysisPrompt(originalQuery: string, gatheredData: any[]): string {
    // Get current date and time for context
    const now = new Date();
    const currentDateTime = now.toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });

    const dataContext = gatheredData.map((item, index) =>
      `Source ${index + 1} (${item.type}): ${item.description}\nQuery: ${item.query}\nData: ${JSON.stringify(item.data, null, 2)}`
    ).join('\n\n---\n\n');

    return `You are an expert information analyst. Analyze the following browsing data and provide a comprehensive answer to the original query.

CURRENT DATE & TIME: ${currentDateTime}
ORIGINAL QUERY: "${originalQuery}"

GATHERED DATA:
${dataContext}

Please provide:
1. A comprehensive answer to the original query
2. Key findings and insights with temporal relevance
3. Any conflicting information found
4. Confidence level in the findings
5. Recommendations for further research if needed
6. Note the recency and relevance of the information found

IMPORTANT: Consider the current date when analyzing the data. Prioritize recent information and note if any data appears outdated. For time-sensitive queries, emphasize the most current findings.

Format your response as a clear, well-structured analysis that directly addresses the user's query with temporal awareness.`;
  }

  /**
   * Call AI for planning
   */
  private async callAIForPlanning(
    prompt: string,
    model: BrowsingModel
  ): Promise<{ success: boolean; content?: string; error?: string }> {
    try {
      const effectiveModelId = this.getEffectiveModelId(model);

      const messages = [
        {
          role: 'user' as const,
          content: prompt
        }
      ];

      let apiUrl: string;
      let headers: Record<string, string>;
      let body: any;

      // Configure API call based on provider (same as BrowsingExecutionService)
      switch (model.provider) {
        case 'openai':
          apiUrl = 'https://api.openai.com/v1/chat/completions';
          headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${model.api_key}`
          };
          body = {
            model: effectiveModelId,
            messages,
            temperature: 0.1, // Low temperature for consistent planning
            max_tokens: 1500
          };
          break;

        case 'google':
          apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';
          headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${model.api_key}`
          };
          body = {
            model: effectiveModelId,
            messages,
            temperature: 0.1,
            max_tokens: 1500
          };
          break;

        case 'anthropic':
          apiUrl = 'https://api.anthropic.com/v1/messages';
          headers = {
            'Content-Type': 'application/json',
            'x-api-key': model.api_key,
            'anthropic-version': '2023-06-01'
          };
          body = {
            model: effectiveModelId,
            messages,
            temperature: 0.1,
            max_tokens: 1500
          };
          break;

        case 'openrouter':
          apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
          headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${model.api_key}`,
            'HTTP-Referer': 'https://roukey.online',
            'X-Title': 'RouKey'
          };
          body = {
            model: effectiveModelId,
            messages,
            temperature: 0.1,
            max_tokens: 1500
          };
          break;

        default:
          throw new Error(`Unsupported provider for planning: ${model.provider}`);
      }

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
        signal: AbortSignal.timeout(30000)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();

      let content: string | undefined;
      if (model.provider === 'anthropic') {
        content = result.content?.[0]?.text;
      } else {
        content = result.choices?.[0]?.message?.content;
      }

      if (!content) {
        throw new Error('No content returned from AI model');
      }

      return { success: true, content };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Call AI for analysis (same as planning but different purpose)
   */
  private async callAIForAnalysis(
    prompt: string,
    model: BrowsingModel
  ): Promise<{ success: boolean; content?: string; error?: string }> {
    return this.callAIForPlanning(prompt, model); // Same implementation
  }

  /**
   * Get effective model ID (same logic as BrowsingExecutionService)
   */
  private getEffectiveModelId(model: BrowsingModel): string {
    if (model.provider.toLowerCase() === 'openrouter') {
      return model.model;
    }
    const parts = model.model.split('/');
    return parts.length > 1 ? parts[parts.length - 1] : model.model;
  }

  /**
   * Parse plan from AI response
   */
  private parsePlanFromAI(aiResponse: string, originalQuery: string): { goal: string; subtasks: BrowsingSubtask[] } {
    try {
      // Try to extract JSON from the response
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      if (!parsed.goal || !parsed.subtasks || !Array.isArray(parsed.subtasks)) {
        throw new Error('Invalid plan structure from AI');
      }

      // Convert AI subtasks to our format
      const subtasks: BrowsingSubtask[] = parsed.subtasks.map((task: any, index: number) => ({
        id: task.id || `ai_task_${Date.now()}_${index}`,
        type: task.type || 'search',
        description: task.description || `Task ${index + 1}`,
        query: task.query || originalQuery,
        status: 'pending' as const,
        priority: task.priority || 5,
        attempts: 0,
        maxAttempts: 3,
        searchTerms: task.searchTerms || [],
        expectedInfo: task.expectedInfo || ''
      }));

      return {
        goal: parsed.goal,
        subtasks
      };

    } catch (error) {
      console.warn('[Smart Browsing] Failed to parse AI plan, using fallback:', error);
      return {
        goal: `Find information about: ${originalQuery}`,
        subtasks: this.createFallbackPlan(originalQuery, 'search')
      };
    }
  }

  /**
   * Synthesize final result from all gathered data
   */
  private async synthesizeFinalResult(
    plan: BrowsingPlan,
    browsingConfig: BrowsingConfig
  ): Promise<string> {
    try {
      // Find the analysis result if available
      const analysisTask = plan.subtasks.find(task =>
        task.type === 'analyze' && task.status === 'completed' && task.result
      );

      if (analysisTask && analysisTask.result?.analysis) {
        return analysisTask.result.analysis;
      }

      // If no analysis task, create a summary from all completed tasks
      const completedTasks = plan.subtasks.filter(task =>
        task.status === 'completed' && task.result
      );

      if (completedTasks.length === 0) {
        return `No information was successfully gathered for the query: "${plan.originalQuery}"`;
      }

      // Create a basic summary
      let summary = `Based on browsing research for "${plan.originalQuery}":\n\n`;

      completedTasks.forEach((task, index) => {
        summary += `${index + 1}. ${task.description}:\n`;
        if (task.result?.results && Array.isArray(task.result.results)) {
          task.result.results.slice(0, 3).forEach((result: any) => {
            summary += `   • ${result.title || 'Result'}\n`;
          });
        } else if (typeof task.result === 'string') {
          summary += `   ${task.result.substring(0, 200)}...\n`;
        }
        summary += '\n';
      });

      return summary;

    } catch (error) {
      console.error('[Smart Browsing] Error synthesizing final result:', error);
      return `Research completed for "${plan.originalQuery}" but encountered errors in synthesis. Please check the individual results.`;
    }
  }

  /**
   * Log the browsing plan for debugging
   */
  private logPlan(plan: BrowsingPlan): void {
    console.log(`[Smart Browsing] 📋 BROWSING PLAN:`);
    console.log(`[Smart Browsing] Goal: ${plan.goal}`);
    console.log(`[Smart Browsing] Subtasks:`);
    plan.subtasks.forEach((subtask, index) => {
      console.log(`[Smart Browsing]   ${index + 1}. [${subtask.type.toUpperCase()}] ${subtask.description}`);
      console.log(`[Smart Browsing]      Query: "${subtask.query}"`);
      console.log(`[Smart Browsing]      Priority: ${subtask.priority}, Status: ${subtask.status}`);
      if (subtask.searchTerms && subtask.searchTerms.length > 0) {
        console.log(`[Smart Browsing]      Alt terms: ${subtask.searchTerms.join(', ')}`);
      }
    });
  }

  /**
   * Create or reuse browser session for complex workflows
   */
  private async ensureBrowserSession(plan: BrowsingPlan, initialUrl?: string): Promise<{ sessionId: string; reconnectUrl: string }> {
    // Check if we have an existing session that's still valid
    if (plan.sessionInfo && plan.sessionInfo.reconnectUrl) {
      const timeSinceLastUse = Date.now() - new Date(plan.sessionInfo.lastUsed).getTime();
      const sessionTimeout = 10 * 60 * 1000; // 10 minutes

      if (timeSinceLastUse < sessionTimeout) {
        console.log(`[Smart Browsing] 🔄 Reusing existing browser session: ${plan.sessionInfo.sessionId}`);

        // Update last used time
        plan.sessionInfo.lastUsed = new Date();

        return {
          sessionId: plan.sessionInfo.sessionId,
          reconnectUrl: plan.sessionInfo.reconnectUrl
        };
      } else {
        console.log(`[Smart Browsing] ⏰ Session expired, creating new session`);
      }
    }

    // Create new session
    console.log(`[Smart Browsing] 🆕 Creating new browser session`);

    const session = await this.browserlessService.createBrowsingSession(
      initialUrl || 'https://www.google.com',
      {
        timeout: 600000, // 10 minutes
        humanLike: true
      }
    );

    // Store session info in plan
    plan.sessionInfo = {
      sessionId: session.sessionId,
      reconnectUrl: session.reconnectUrl,
      lastUsed: new Date(),
      activeWorkflow: true
    };

    console.log(`[Smart Browsing] ✅ Created browser session: ${session.sessionId}`);

    return session;
  }

  /**
   * Check if subtask requires persistent session
   */
  private requiresPersistentSession(subtask: BrowsingSubtask): boolean {
    const description = subtask.description.toLowerCase();
    const query = subtask.query.toLowerCase();

    // Tasks that benefit from session persistence
    const sessionKeywords = [
      'form', 'booking', 'reservation', 'login', 'multi-step',
      'workflow', 'navigation', 'complex', 'automation'
    ];

    // Flight booking always benefits from session persistence
    const flightBookingKeywords = [
      'flight', 'airline', 'booking', 'search flight', 'travel'
    ];

    return sessionKeywords.some(keyword =>
      description.includes(keyword) || query.includes(keyword)
    ) || flightBookingKeywords.some(keyword =>
      description.includes(keyword) || query.includes(keyword)
    );
  }
}
