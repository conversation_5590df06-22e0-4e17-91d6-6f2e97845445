/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/tools/web-browsing/route";
exports.ids = ["app/api/tools/web-browsing/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fweb-browsing%2Froute&page=%2Fapi%2Ftools%2Fweb-browsing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fweb-browsing%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fweb-browsing%2Froute&page=%2Fapi%2Ftools%2Fweb-browsing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fweb-browsing%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_tools_web_browsing_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/tools/web-browsing/route.ts */ \"(rsc)/./src/app/api/tools/web-browsing/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/tools/web-browsing/route\",\n        pathname: \"/api/tools/web-browsing\",\n        filename: \"route\",\n        bundlePath: \"app/api/tools/web-browsing/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\tools\\\\web-browsing\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_tools_web_browsing_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fweb-browsing%2Froute&page=%2Fapi%2Ftools%2Fweb-browsing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fweb-browsing%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/tools/web-browsing/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/tools/web-browsing/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, url, query, searchEngine = 'google', extractionType = 'content', customSelector, timeout } = body;\n        if (!action) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Action is required'\n            }, {\n                status: 400\n            });\n        }\n        const browserless = _lib_browserless__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getInstance();\n        let result;\n        switch(action){\n            case 'navigate':\n                if (!url) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'URL is required for navigate action'\n                    }, {\n                        status: 400\n                    });\n                }\n                result = await browserless.navigateAndExtract(url, customSelector);\n                break;\n            case 'search':\n                if (!query) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Query is required for search action'\n                    }, {\n                        status: 400\n                    });\n                }\n                result = await browserless.searchAndExtractUnblocked(query, searchEngine);\n                break;\n            case 'screenshot':\n                if (!url) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'URL is required for screenshot action'\n                    }, {\n                        status: 400\n                    });\n                }\n                result = await browserless.takeScreenshot(url);\n                break;\n            case 'custom':\n                if (!url) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'URL is required for custom action'\n                    }, {\n                        status: 400\n                    });\n                }\n                // Custom browsing with user-provided code\n                const customCode = body.code;\n                if (!customCode) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Code is required for custom action'\n                    }, {\n                        status: 400\n                    });\n                }\n                result = await browserless.executeFunction(customCode, {\n                    url,\n                    ...body.context\n                }, {\n                    timeout\n                });\n                break;\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `Unknown action: ${action}`\n                }, {\n                    status: 400\n                });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.data,\n            action,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Web browsing API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Web browsing failed',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET endpoint to check service status and stats\nasync function GET() {\n    try {\n        const browserless = _lib_browserless__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getInstance();\n        const stats = browserless.getStats();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'operational',\n            service: 'web-browsing',\n            stats,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Web browsing status check error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'error',\n            service: 'web-browsing',\n            error: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/tools/web-browsing/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browserless.ts":
/*!********************************!*\
  !*** ./src/lib/browserless.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Browserless.io API service with key rotation\n// Similar to Jina API key rotation system\nclass BrowserlessService {\n    constructor(){\n        this.apiKeys = [];\n        this.currentKeyIndex = 0;\n        this.keyUsageCount = new Map();\n        this.keyErrors = new Map();\n        this.MAX_RETRIES = 3;\n        this.ERROR_THRESHOLD = 5;\n        this.ENDPOINT = 'https://production-sfo.browserless.io';\n        this.initializeKeys();\n        this.testConnectivity();\n    }\n    static getInstance() {\n        if (!BrowserlessService.instance) {\n            BrowserlessService.instance = new BrowserlessService();\n        }\n        return BrowserlessService.instance;\n    }\n    initializeKeys() {\n        // Load all Browserless API keys from environment (similar to Jina pattern)\n        this.apiKeys = [\n            process.env.BROWSERLESS_API_KEY,\n            process.env.BROWSERLESS_API_KEY_2,\n            process.env.BROWSERLESS_API_KEY_3,\n            process.env.BROWSERLESS_API_KEY_4,\n            process.env.BROWSERLESS_API_KEY_5,\n            process.env.BROWSERLESS_API_KEY_6,\n            process.env.BROWSERLESS_API_KEY_7,\n            process.env.BROWSERLESS_API_KEY_8,\n            process.env.BROWSERLESS_API_KEY_9,\n            process.env.BROWSERLESS_API_KEY_10\n        ].filter(Boolean);\n        if (this.apiKeys.length === 0) {\n            console.error('No Browserless API keys found in environment variables');\n            return;\n        }\n        console.log(`[Browserless] Initialized with ${this.apiKeys.length} API keys`);\n        // Initialize usage tracking\n        this.apiKeys.forEach((key)=>{\n            this.keyUsageCount.set(key, 0);\n            this.keyErrors.set(key, 0);\n        });\n    }\n    /**\n   * Test connectivity to Browserless service\n   */ async testConnectivity() {\n        if (this.apiKeys.length === 0) {\n            console.warn('[Browserless] No API keys available for connectivity test');\n            return;\n        }\n        try {\n            const testKey = this.apiKeys[0];\n            const testUrl = `${this.ENDPOINT}/function?token=${testKey}`;\n            // Simple connectivity test with proper function format\n            const testCode = `\n        export default async function ({ page }) {\n          return {\n            status: \"connectivity-test-success\",\n            timestamp: new Date().toISOString()\n          };\n        }\n      `;\n            const response = await fetch(testUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/javascript'\n                },\n                body: testCode,\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            if (response.ok) {\n                const result = await response.text();\n                console.log('[Browserless] ✅ Connectivity test successful:', result);\n            } else {\n                const errorText = await response.text();\n                console.warn(`[Browserless] ⚠️ Connectivity test failed with status: ${response.status}`);\n                console.warn(`[Browserless] Error details: ${errorText}`);\n                console.warn(`[Browserless] Using key: ${testKey.substring(0, 8)}...`);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.warn(`[Browserless] ⚠️ Connectivity test failed: ${errorMessage}`);\n            // Check for specific network errors\n            if (errorMessage.includes('ENOTFOUND')) {\n                console.error('[Browserless] 🌐 DNS resolution failed - check if chrome.browserless.io is accessible');\n            } else if (errorMessage.includes('ECONNRESET')) {\n                console.error('[Browserless] 🔌 Connection reset - possible network or service issue');\n            }\n        }\n    }\n    getNextApiKey() {\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Browserless API keys available');\n        }\n        // Find the key with lowest usage and errors\n        let bestKey = this.apiKeys[0];\n        let bestScore = this.calculateKeyScore(bestKey);\n        for (const key of this.apiKeys){\n            const score = this.calculateKeyScore(key);\n            if (score < bestScore) {\n                bestKey = key;\n                bestScore = score;\n            }\n        }\n        return bestKey;\n    }\n    calculateKeyScore(key) {\n        const usage = this.keyUsageCount.get(key) || 0;\n        const errors = this.keyErrors.get(key) || 0;\n        // Higher score = worse key (more usage + more errors)\n        return usage + errors * 10;\n    }\n    incrementKeyUsage(key) {\n        const currentUsage = this.keyUsageCount.get(key) || 0;\n        this.keyUsageCount.set(key, currentUsage + 1);\n    }\n    incrementKeyError(key) {\n        const currentErrors = this.keyErrors.get(key) || 0;\n        this.keyErrors.set(key, currentErrors + 1);\n    }\n    isKeyHealthy(key) {\n        const errors = this.keyErrors.get(key) || 0;\n        return errors < this.ERROR_THRESHOLD;\n    }\n    getHealthyKeys() {\n        return this.apiKeys.filter((key)=>this.isKeyHealthy(key));\n    }\n    async executeFunction(code, context, config) {\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            // Reset error counts if all keys are unhealthy\n            this.keyErrors.clear();\n            this.apiKeys.forEach((key)=>this.keyErrors.set(key, 0));\n            console.log('All Browserless keys were unhealthy, resetting error counts');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const response = await this.makeRequest(apiKey, code, context, config);\n                // Success - return the response\n                return response;\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless attempt ${attempt + 1} failed:`, error);\n                // If it's a rate limit or quota error, mark the key as having an error\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless API attempts failed');\n    }\n    async makeRequest(apiKey, code, context, config) {\n        const url = `${this.ENDPOINT}/function?token=${apiKey}`;\n        const requestBody = context ? {\n            code,\n            context\n        } : code;\n        const headers = {\n            'Content-Type': context ? 'application/json' : 'application/javascript',\n            'User-Agent': config?.userAgent || 'RouKey-Browser-Agent/1.0'\n        };\n        const response = await fetch(url, {\n            method: 'POST',\n            headers,\n            body: context ? JSON.stringify(requestBody) : code,\n            signal: AbortSignal.timeout(config?.timeout || 30000)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Browserless API error: ${response.status} - ${errorText}`);\n        }\n        const result = await response.json();\n        return result;\n    }\n    isRateLimitError(error) {\n        const message = error.message.toLowerCase();\n        return message.includes('rate limit') || message.includes('quota') || message.includes('429') || message.includes('too many requests');\n    }\n    // Convenience methods for common browser tasks\n    async navigateAndExtract(url, selector) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n        \n        const title = await page.title();\n        const content = ${selector ? `await page.$eval(\"${selector}\", el => el.textContent || el.innerText)` : 'await page.evaluate(() => document.body.innerText)'};\n        \n        return {\n          data: {\n            url: \"${url}\",\n            title,\n            content: content?.trim() || \"\"\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced BrowserQL automation for complex browsing tasks\n   * Supports form filling, CAPTCHA solving, multi-step workflows, and state-of-the-art browsing\n   */ async executeBrowserQLAutomation(automationScript, options = {}) {\n        const { timeout = 60000, humanLike = true, solveCaptcha = true, sessionId, screenshots = false } = options;\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            throw new Error('No healthy Browserless API keys available');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.maxRetries; attempt++){\n            try {\n                const currentKey = this.getNextApiKey();\n                console.log(`[Browserless] BrowserQL automation attempt ${attempt + 1} with key: ${currentKey.name}`);\n                // Build the endpoint URL\n                let endpoint = `https://production-sfo.browserless.io/chromium/bql?token=${currentKey.key}`;\n                if (sessionId) {\n                    endpoint += `&sessionId=${sessionId}`;\n                }\n                const response = await fetch(endpoint, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'User-Agent': 'RouKey-Browser-Automation/1.0'\n                    },\n                    body: JSON.stringify({\n                        query: automationScript,\n                        variables: {\n                            timeout,\n                            humanLike,\n                            solveCaptcha,\n                            screenshots\n                        }\n                    }),\n                    signal: AbortSignal.timeout(timeout + 10000) // Add buffer to timeout\n                });\n                if (!response.ok) {\n                    throw new Error(`BrowserQL automation failed: ${response.status} ${response.statusText}`);\n                }\n                const result = await response.json();\n                if (result.errors && result.errors.length > 0) {\n                    throw new Error(`BrowserQL errors: ${result.errors.map((e)=>e.message).join(', ')}`);\n                }\n                console.log(`[Browserless] ✅ BrowserQL automation successful`);\n                return {\n                    data: result.data,\n                    type: \"application/json\",\n                    sessionId: result.sessionId,\n                    screenshots: result.screenshots\n                };\n            } catch (error) {\n                lastError = error;\n                console.error(`[Browserless] BrowserQL automation attempt ${attempt + 1} failed:`, error);\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All BrowserQL automation attempts failed');\n    }\n    /**\n   * Create a persistent browsing session for complex multi-step workflows\n   */ async createBrowsingSession(initialUrl, options = {}) {\n        const { timeout = 300000, humanLike = true, blockResources = [\n            '*.png',\n            '*.jpg',\n            '*.gif',\n            '*.mp4',\n            '*.css'\n        ] } = options;\n        const sessionScript = `\n      mutation CreateBrowsingSession {\n        ${blockResources.length > 0 ? `\n        setRequestInterception(enabled: true)\n        reject(patterns: ${JSON.stringify(blockResources)})\n        ` : ''}\n\n        goto(url: \"${initialUrl}\", waitUntil: networkIdle) {\n          status\n          time\n        }\n\n        ${humanLike ? `\n        # Add human-like behavior\n        waitForTimeout(time: ${Math.floor(Math.random() * 2000) + 1000}) {\n          time\n        }\n        ` : ''}\n\n        reconnect(timeout: ${timeout}) {\n          BrowserQLEndpoint\n        }\n      }\n    `;\n        const result = await this.executeBrowserQLAutomation(sessionScript, {\n            timeout\n        });\n        return {\n            sessionId: result.sessionId || 'default',\n            reconnectUrl: result.data.reconnect.BrowserQLEndpoint\n        };\n    }\n    /**\n   * Advanced form filling with human-like behavior\n   */ async fillFormAdvanced(sessionUrl, formData, submitSelector) {\n        const formScript = `\n      mutation FillFormAdvanced {\n        ${formData.map((field, index)=>{\n            const delay = field.delay || [\n                50,\n                150\n            ];\n            return `\n            field${index}: ${field.type === 'select' ? 'select' : field.type === 'checkbox' || field.type === 'radio' ? 'click' : 'type'}(\n              selector: \"${field.selector}\"\n              ${field.type !== 'checkbox' && field.type !== 'radio' ? `text: \"${field.value}\"` : ''}\n              ${field.type === 'text' || field.type === 'email' || field.type === 'password' ? `delay: [${delay[0]}, ${delay[1]}]` : ''}\n              visible: true\n            ) {\n              time\n              ${field.type === 'text' || field.type === 'email' || field.type === 'password' ? 'text' : 'x'}\n            }\n          `;\n        }).join('\\n')}\n\n        ${submitSelector ? `\n        submitForm: click(\n          selector: \"${submitSelector}\"\n          visible: true\n        ) {\n          time\n          x\n          y\n        }\n        ` : ''}\n\n        # Wait for any page changes after form submission\n        waitForTimeout(time: 2000) {\n          time\n        }\n      }\n    `;\n        return await this.executeBrowserQLWithSession(sessionUrl, formScript);\n    }\n    /**\n   * Execute BrowserQL script with existing session\n   */ async executeBrowserQLWithSession(sessionUrl, script) {\n        try {\n            const response = await fetch(sessionUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: script\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`Session execution failed: ${response.status} ${response.statusText}`);\n            }\n            const result = await response.json();\n            if (result.errors && result.errors.length > 0) {\n                throw new Error(`Session errors: ${result.errors.map((e)=>e.message).join(', ')}`);\n            }\n            return result;\n        } catch (error) {\n            console.error('[Browserless] Session execution failed:', error);\n            throw error;\n        }\n    }\n    async searchAndExtractUnblocked(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            throw new Error('No healthy Browserless API keys available');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const url = `${this.ENDPOINT}/unblock?token=${apiKey}`;\n                const requestBody = {\n                    url: searchUrl,\n                    content: true,\n                    browserWSEndpoint: false,\n                    cookies: false,\n                    screenshot: false,\n                    waitForSelector: {\n                        selector: 'h3, .g h3, .LC20lb, .b_algo h2',\n                        timeout: 10000\n                    }\n                };\n                const response = await fetch(url, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'User-Agent': 'RouKey-Browser-Agent/1.0'\n                    },\n                    body: JSON.stringify(requestBody),\n                    signal: AbortSignal.timeout(60000)\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`Browserless unblock API error: ${response.status} - ${errorText}`);\n                }\n                const result = await response.json();\n                if (result.content) {\n                    // Parse the HTML content to extract search results\n                    const searchResults = this.parseSearchResults(result.content, searchEngine, query);\n                    return {\n                        data: {\n                            query,\n                            searchEngine,\n                            results: searchResults,\n                            timestamp: new Date().toISOString(),\n                            debug: {\n                                pageTitle: 'Unblocked search',\n                                pageUrl: searchUrl,\n                                totalElements: searchResults.length,\n                                usedSelector: 'unblock-api',\n                                extractedCount: searchResults.length\n                            }\n                        },\n                        type: \"application/json\"\n                    };\n                } else {\n                    throw new Error('No content returned from unblock API');\n                }\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless unblock attempt ${attempt + 1} failed:`, error);\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless unblock API attempts failed');\n    }\n    /**\n   * Handle infinite scroll and pagination automatically\n   */ async handleInfiniteScroll(sessionUrl, options = {}) {\n        const { maxScrolls = 10, scrollDelay = 2000, contentSelector = 'body', stopCondition = 'no-more-content' } = options;\n        const scrollScript = `\n      mutation HandleInfiniteScroll {\n        # Get initial content count\n        initialContent: text(selector: \"${contentSelector}\") {\n          text\n        }\n\n        # Perform scrolling with content monitoring\n        ${Array.from({\n            length: maxScrolls\n        }, (_, i)=>`\n          scroll${i}: scroll(\n            direction: down\n            distance: 1000\n          ) {\n            time\n          }\n\n          waitAfterScroll${i}: waitForTimeout(time: ${scrollDelay}) {\n            time\n          }\n\n          contentCheck${i}: text(selector: \"${contentSelector}\") {\n            text\n          }\n        `).join('\\n')}\n\n        # Get final content\n        finalContent: text(selector: \"${contentSelector}\") {\n          text\n        }\n      }\n    `;\n        return await this.executeBrowserQLWithSession(sessionUrl, scrollScript);\n    }\n    /**\n   * Advanced content extraction with multiple strategies\n   */ async extractContentAdvanced(sessionUrl, extractionRules) {\n        const extractionScript = `\n      mutation ExtractContentAdvanced {\n        ${extractionRules.map((rule, index)=>{\n            if (rule.type === 'screenshot') {\n                return `\n              ${rule.name}: screenshot(\n                selector: \"${rule.selector}\"\n                fullPage: false\n              ) {\n                data\n              }\n            `;\n            } else if (rule.type === 'attribute') {\n                return `\n              ${rule.name}: html(\n                selector: \"${rule.selector}\"\n                visible: true\n              ) {\n                html\n              }\n            `;\n            } else {\n                return `\n              ${rule.name}: ${rule.type}(\n                selector: \"${rule.selector}\"\n                visible: true\n              ) {\n                ${rule.type}\n              }\n            `;\n            }\n        }).join('\\n')}\n      }\n    `;\n        return await this.executeBrowserQLWithSession(sessionUrl, extractionScript);\n    }\n    /**\n   * Complex multi-step automation workflow\n   */ async executeComplexWorkflow(initialUrl, workflow) {\n        // Create session first\n        const session = await this.createBrowsingSession(initialUrl, {\n            timeout: 600000,\n            humanLike: true\n        });\n        const workflowScript = `\n      mutation ComplexWorkflow {\n        ${workflow.map((step, index)=>{\n            switch(step.type){\n                case 'navigate':\n                    return `\n                step${index}_navigate: goto(\n                  url: \"${step.params.url}\"\n                  waitUntil: ${step.params.waitUntil || 'networkIdle'}\n                ) {\n                  status\n                  time\n                }\n              `;\n                case 'click':\n                    return `\n                step${index}_click: click(\n                  selector: \"${step.params.selector}\"\n                  visible: true\n                  ${step.params.timeout ? `timeout: ${step.params.timeout}` : ''}\n                ) {\n                  time\n                  x\n                  y\n                }\n              `;\n                case 'type':\n                    return `\n                step${index}_type: type(\n                  selector: \"${step.params.selector}\"\n                  text: \"${step.params.text}\"\n                  ${step.params.delay ? `delay: [${step.params.delay[0]}, ${step.params.delay[1]}]` : ''}\n                  visible: true\n                ) {\n                  time\n                  text\n                }\n              `;\n                case 'wait':\n                    return `\n                step${index}_wait: waitForTimeout(time: ${step.params.time || 1000}) {\n                  time\n                }\n              `;\n                case 'extract':\n                    return `\n                step${index}_extract: ${step.params.type || 'text'}(\n                  selector: \"${step.params.selector}\"\n                  visible: true\n                ) {\n                  ${step.params.type || 'text'}\n                }\n              `;\n                case 'scroll':\n                    return `\n                step${index}_scroll: scroll(\n                  direction: ${step.params.direction || 'down'}\n                  distance: ${step.params.distance || 500}\n                ) {\n                  time\n                }\n              `;\n                case 'captcha':\n                    return `\n                step${index}_captcha: verify(\n                  type: ${step.params.type || 'hcaptcha'}\n                ) {\n                  solved\n                  time\n                }\n              `;\n                case 'screenshot':\n                    return `\n                step${index}_screenshot: screenshot(\n                  ${step.params.selector ? `selector: \"${step.params.selector}\"` : ''}\n                  fullPage: ${step.params.fullPage || false}\n                ) {\n                  data\n                }\n              `;\n                default:\n                    return `# Unknown step type: ${step.type}`;\n            }\n        }).join('\\n')}\n\n        # Final status check\n        finalStatus: html(visible: false) {\n          html\n        }\n      }\n    `;\n        try {\n            const result = await this.executeBrowserQLWithSession(session.reconnectUrl, workflowScript);\n            return {\n                ...result,\n                sessionId: session.sessionId,\n                reconnectUrl: session.reconnectUrl\n            };\n        } catch (error) {\n            console.error('[Browserless] Complex workflow failed:', error);\n            throw error;\n        }\n    }\n    parseSearchResults(htmlContent, searchEngine, query) {\n        // Modern search result parsing with comprehensive snippet extraction\n        // Updated for 2025 Google/Bing HTML structures to provide accurate, current information\n        const results = [];\n        if (searchEngine === 'google') {\n            // Updated Google search result patterns for 2025\n            // Google now uses more dynamic class names and data attributes\n            const modernResultPatterns = [\n                // Main search result containers (2025 patterns)\n                /<div[^>]*data-ved=\"[^\"]*\"[^>]*class=\"[^\"]*g[^\"]*\"[^>]*>(.*?)<\\/div>/gis,\n                /<div[^>]*class=\"[^\"]*MjjYud[^\"]*\"[^>]*>(.*?)<\\/div>/gis,\n                /<div[^>]*class=\"[^\"]*kvH3mc[^\"]*\"[^>]*>(.*?)<\\/div>/gis,\n                /<div[^>]*class=\"[^\"]*N54PNb[^\"]*\"[^>]*>(.*?)<\\/div>/gis\n            ];\n            // Modern title patterns\n            const titlePatterns = [\n                /<h3[^>]*class=\"[^\"]*LC20lb[^\"]*\"[^>]*>([^<]+)<\\/h3>/i,\n                /<h3[^>]*class=\"[^\"]*DKV0Md[^\"]*\"[^>]*>([^<]+)<\\/h3>/i,\n                /<h3[^>]*>([^<]+)<\\/h3>/i\n            ];\n            // Modern link patterns\n            const linkPatterns = [\n                /<a[^>]*href=\"([^\"]*)\"[^>]*data-ved=\"[^\"]*\"[^>]*>/i,\n                /<a[^>]*href=\"([^\"]*)\"[^>]*>/i\n            ];\n            // Updated snippet patterns for 2025\n            const snippetPatterns = [\n                // Current Google snippet classes\n                /<span[^>]*class=\"[^\"]*aCOpRe[^\"]*\"[^>]*>([^<]+)<\\/span>/i,\n                /<div[^>]*class=\"[^\"]*VwiC3b[^\"]*\"[^>]*>([^<]+)<\\/div>/i,\n                /<span[^>]*class=\"[^\"]*st[^\"]*\"[^>]*>([^<]+)<\\/span>/i,\n                /<div[^>]*data-content-feature=\"snippet\"[^>]*>([^<]+)<\\/div>/i,\n                /<span[^>]*data-snippet=\"[^\"]*\"[^>]*>([^<]+)<\\/span>/i,\n                // Fallback patterns\n                /<div[^>]*class=\"[^\"]*IsZvec[^\"]*\"[^>]*>([^<]+)<\\/div>/i,\n                /<span[^>]*class=\"[^\"]*hgKElc[^\"]*\"[^>]*>([^<]+)<\\/span>/i\n            ];\n            // Try each result container pattern\n            for (const containerPattern of modernResultPatterns){\n                let containerMatch;\n                while((containerMatch = containerPattern.exec(htmlContent)) !== null && results.length < 10){\n                    const containerContent = containerMatch[1];\n                    // Extract title using multiple patterns\n                    let title = '';\n                    for (const titlePattern of titlePatterns){\n                        const titleMatch = titlePattern.exec(containerContent);\n                        if (titleMatch) {\n                            title = titleMatch[1].trim();\n                            break;\n                        }\n                    }\n                    // Extract link using multiple patterns\n                    let link = '';\n                    for (const linkPattern of linkPatterns){\n                        const linkMatch = linkPattern.exec(containerContent);\n                        if (linkMatch) {\n                            link = linkMatch[1];\n                            break;\n                        }\n                    }\n                    if (title && link && link.startsWith('http') && !link.includes('google.com') && !link.includes('/search?') && !link.includes('webcache.googleusercontent.com')) {\n                        // Extract snippet using multiple patterns\n                        let snippet = '';\n                        for (const snippetPattern of snippetPatterns){\n                            const snippetMatch = snippetPattern.exec(containerContent);\n                            if (snippetMatch) {\n                                snippet = snippetMatch[1].trim();\n                                // Clean up HTML entities and normalize whitespace\n                                snippet = snippet.replace(/&[^;]+;/g, ' ').replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim();\n                                break;\n                            }\n                        }\n                        // Enhanced fallback snippet extraction\n                        if (!snippet) {\n                            // Try to extract any meaningful text from the container\n                            const cleanText = containerContent.replace(/<script[^>]*>.*?<\\/script>/gis, '').replace(/<style[^>]*>.*?<\\/style>/gis, '').replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim();\n                            const sentences = cleanText.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n                            if (sentences.length > 0) {\n                                snippet = sentences[0].trim().substring(0, 200) + '...';\n                            } else {\n                                const words = cleanText.split(' ').filter((w)=>w.length > 2);\n                                if (words.length > 10) {\n                                    snippet = words.slice(0, 25).join(' ') + '...';\n                                }\n                            }\n                        }\n                        results.push({\n                            title,\n                            link,\n                            snippet: snippet || 'No description available',\n                            searchEngine: 'google',\n                            query,\n                            timestamp: new Date().toISOString(),\n                            relevanceScore: snippet.length > 50 ? 0.9 : 0.7 // Higher score for better snippets\n                        });\n                    }\n                }\n                if (results.length > 0) break; // Stop if we found results with this pattern\n            }\n        } else {\n            // Updated Bing search result patterns for 2025\n            const bingContainerPatterns = [\n                /<li[^>]*class=\"[^\"]*b_algo[^\"]*\"[^>]*>(.*?)<\\/li>/gis,\n                /<div[^>]*class=\"[^\"]*b_algoheader[^\"]*\"[^>]*>(.*?)<\\/div>/gis\n            ];\n            const bingTitlePatterns = [\n                /<h2[^>]*><a[^>]*href=\"([^\"]*)\"[^>]*>([^<]+)<\\/a><\\/h2>/i,\n                /<a[^>]*href=\"([^\"]*)\"[^>]*><h2[^>]*>([^<]+)<\\/h2><\\/a>/i\n            ];\n            const bingSnippetPatterns = [\n                /<p[^>]*class=\"[^\"]*b_lineclamp[^\"]*\"[^>]*>([^<]+)<\\/p>/i,\n                /<div[^>]*class=\"[^\"]*b_caption[^\"]*\"[^>]*>([^<]+)<\\/div>/i,\n                /<p[^>]*>([^<]+)<\\/p>/i\n            ];\n            for (const containerPattern of bingContainerPatterns){\n                let containerMatch;\n                while((containerMatch = containerPattern.exec(htmlContent)) !== null && results.length < 10){\n                    const containerContent = containerMatch[1];\n                    let title = '', link = '';\n                    for (const titlePattern of bingTitlePatterns){\n                        const titleMatch = titlePattern.exec(containerContent);\n                        if (titleMatch) {\n                            link = titleMatch[1];\n                            title = titleMatch[2].trim();\n                            break;\n                        }\n                    }\n                    if (title && link) {\n                        let snippet = '';\n                        for (const snippetPattern of bingSnippetPatterns){\n                            const snippetMatch = snippetPattern.exec(containerContent);\n                            if (snippetMatch) {\n                                snippet = snippetMatch[1].trim().replace(/&[^;]+;/g, ' ').replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim();\n                                break;\n                            }\n                        }\n                        results.push({\n                            title,\n                            link,\n                            snippet: snippet || 'No description available',\n                            searchEngine: 'bing',\n                            query,\n                            timestamp: new Date().toISOString(),\n                            relevanceScore: snippet.length > 50 ? 0.9 : 0.7\n                        });\n                    }\n                }\n                if (results.length > 0) break;\n            }\n        }\n        // Sort results by relevance score (better snippets first)\n        results.sort((a, b)=>(b.relevanceScore || 0) - (a.relevanceScore || 0));\n        console.log(`[Browserless] Parsed ${results.length} search results with enhanced snippets for query: \"${query}\"`);\n        return results.slice(0, 8); // Return top 8 most relevant results\n    }\n    async searchAndExtract(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const code = `\n      export default async function ({ page }) {\n        // Set a realistic user agent and headers\n        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');\n        await page.setExtraHTTPHeaders({\n          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n          'Accept-Language': 'en-US,en;q=0.5',\n          'Accept-Encoding': 'gzip, deflate',\n          'DNT': '1',\n          'Connection': 'keep-alive',\n          'Upgrade-Insecure-Requests': '1',\n        });\n\n        await page.goto(\"${searchUrl}\", { waitUntil: 'networkidle0' });\n\n        // Set up CAPTCHA solving\n        const cdp = await page.createCDPSession();\n\n        // Check for CAPTCHA and solve if found\n        let captchaFound = false;\n        cdp.on('Browserless.captchaFound', () => {\n          console.log('CAPTCHA detected on search page');\n          captchaFound = true;\n        });\n\n        // Wait a moment to see if CAPTCHA is detected\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        if (captchaFound) {\n          console.log('Attempting to solve CAPTCHA...');\n          try {\n            const { solved, error } = await cdp.send('Browserless.solveCaptcha');\n            console.log('CAPTCHA solving result:', { solved, error });\n\n            if (solved) {\n              console.log('CAPTCHA solved successfully');\n              // Wait for page to reload after CAPTCHA solving\n              await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 10000 }).catch(() => {\n                console.log('No navigation after CAPTCHA solve, continuing...');\n              });\n            } else {\n              console.log('CAPTCHA solving failed:', error);\n            }\n          } catch (captchaError) {\n            console.log('CAPTCHA solving error:', captchaError);\n          }\n        }\n\n        // Wait for search results to load with multiple fallback selectors\n        let resultsLoaded = false;\n        const googleSelectors = ['[data-ved]', 'h3', '.g h3', '.LC20lb', '.DKV0Md', '#search h3'];\n        const bingSelectors = ['.b_algo', '.b_algo h2', 'h2 a'];\n\n        const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n\n        for (const selector of selectorsToTry) {\n          try {\n            await page.waitForSelector(selector, { timeout: 3000 });\n            resultsLoaded = true;\n            console.log('Found results with selector:', selector);\n            break;\n          } catch (e) {\n            console.log('Selector failed:', selector);\n            continue;\n          }\n        }\n\n        if (!resultsLoaded) {\n          // Give it one more chance with a longer timeout on the most common selector\n          try {\n            await page.waitForSelector('${searchEngine === 'google' ? 'h3' : '.b_algo'}', { timeout: 5000 });\n          } catch (e) {\n            console.log('All selectors failed, proceeding anyway...');\n          }\n        }\n\n        const results = await page.evaluate(() => {\n          console.log('Starting search results extraction...');\n\n          // Try multiple selectors for extracting results\n          const googleSelectors = [\n            '[data-ved] h3',\n            'h3',\n            '.g h3',\n            '.LC20lb',\n            '.DKV0Md',\n            '#search h3',\n            '.yuRUbf h3',\n            'a h3',\n            '[role=\"heading\"]'\n          ];\n          const bingSelectors = ['.b_algo h2', '.b_algo h2 a', 'h2 a'];\n\n          const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n          let elements = [];\n          let usedSelector = '';\n\n          // Debug: Log page content\n          console.log('Page title:', document.title);\n          console.log('Page URL:', window.location.href);\n          console.log('Page body preview:', document.body.innerText.substring(0, 500));\n\n          for (const selector of selectorsToTry) {\n            elements = document.querySelectorAll(selector);\n            console.log('Trying selector:', selector, 'found:', elements.length, 'elements');\n            if (elements.length > 0) {\n              usedSelector = selector;\n              console.log('Found', elements.length, 'results with selector:', selector);\n              break;\n            }\n          }\n\n          // If no results found, try a more generic approach\n          if (elements.length === 0) {\n            console.log('No results with specific selectors, trying generic approach...');\n            // Try to find any links that look like search results\n            const allLinks = document.querySelectorAll('a[href*=\"/url?\"]');\n            console.log('Found', allLinks.length, 'Google result links');\n\n            if (allLinks.length > 0) {\n              elements = Array.from(allLinks).map(link => {\n                const h3 = link.querySelector('h3');\n                return h3 || link;\n              }).filter(el => el && el.textContent?.trim());\n              usedSelector = 'a[href*=\"/url?\"] h3 (fallback)';\n              console.log('Using fallback approach, found', elements.length, 'elements');\n            }\n          }\n\n          const extractedResults = Array.from(elements).slice(0, 5).map(el => {\n            const title = el.textContent?.trim() || '';\n            let link = '';\n\n            // Try to get the link\n            if (el.href) {\n              link = el.href;\n            } else {\n              const closestLink = el.closest('a');\n              if (closestLink) {\n                link = closestLink.href;\n              }\n            }\n\n            return { title, link };\n          }).filter(item => item.title && item.link);\n\n          console.log('Final results:', extractedResults.length, 'items using selector:', usedSelector);\n\n          return {\n            results: extractedResults,\n            debug: {\n              pageTitle: document.title,\n              pageUrl: window.location.href,\n              totalElements: elements.length,\n              usedSelector: usedSelector || 'none',\n              extractedCount: extractedResults.length\n            }\n          };\n        });\n\n        return {\n          data: {\n            query: \"${query}\",\n            searchEngine: \"${searchEngine}\",\n            results: results.results,\n            timestamp: new Date().toISOString(),\n            debug: results.debug\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code, null, {\n            timeout: 60000 // Increase timeout for CAPTCHA solving\n        });\n    }\n    async takeScreenshot(url, options) {\n        const fullPage = options?.fullPage ?? false;\n        const selector = options?.selector || '';\n        const quality = options?.quality || 80;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        let screenshot;\n        if (\"${selector}\") {\n          // Screenshot specific element\n          const element = await page.waitForSelector(\"${selector}\", { timeout: 10000 });\n          screenshot = await element.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n        } else {\n          // Screenshot full page or viewport\n          screenshot = await page.screenshot({\n            encoding: 'base64',\n            fullPage: ${fullPage},\n            type: 'png',\n            quality: ${quality}\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            screenshot: screenshot,\n            selector: \"${selector}\",\n            fullPage: ${fullPage},\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced form filling with intelligent field detection\n   */ async fillForm(url, formData, options) {\n        const submitAfterFill = options?.submitAfterFill ?? false;\n        const waitForNavigation = options?.waitForNavigation ?? false;\n        const formSelector = options?.formSelector || 'form';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const formData = ${JSON.stringify(formData)};\n        const results = [];\n\n        // Wait for form to be present\n        await page.waitForSelector(\"${formSelector}\", { timeout: 10000 });\n\n        // Fill each field intelligently\n        for (const [fieldName, value] of Object.entries(formData)) {\n          try {\n            // Try multiple selector strategies\n            const selectors = [\n              \\`input[name=\"\\${fieldName}\"]\\`,\n              \\`input[id=\"\\${fieldName}\"]\\`,\n              \\`textarea[name=\"\\${fieldName}\"]\\`,\n              \\`select[name=\"\\${fieldName}\"]\\`,\n              \\`input[placeholder*=\"\\${fieldName}\"]\\`,\n              \\`input[aria-label*=\"\\${fieldName}\"]\\`,\n              \\`[data-testid=\"\\${fieldName}\"]\\`\n            ];\n\n            let filled = false;\n            for (const selector of selectors) {\n              const elements = await page.$$(selector);\n              if (elements.length > 0) {\n                const element = elements[0];\n                const tagName = await element.evaluate(el => el.tagName.toLowerCase());\n\n                if (tagName === 'select') {\n                  await element.selectOption(value.toString());\n                } else if (tagName === 'input') {\n                  const inputType = await element.getAttribute('type');\n                  if (inputType === 'checkbox' || inputType === 'radio') {\n                    if (value) await element.check();\n                  } else {\n                    await element.fill(value.toString());\n                  }\n                } else {\n                  await element.fill(value.toString());\n                }\n\n                results.push({\n                  field: fieldName,\n                  selector: selector,\n                  value: value,\n                  success: true\n                });\n                filled = true;\n                break;\n              }\n            }\n\n            if (!filled) {\n              results.push({\n                field: fieldName,\n                value: value,\n                success: false,\n                error: 'Field not found'\n              });\n            }\n          } catch (error) {\n            results.push({\n              field: fieldName,\n              value: value,\n              success: false,\n              error: error.message\n            });\n          }\n        }\n\n        let submitResult = null;\n        if (${submitAfterFill}) {\n          try {\n            const submitButton = await page.$('input[type=\"submit\"], button[type=\"submit\"], button:has-text(\"Submit\")');\n            if (submitButton) {\n              ${waitForNavigation ? 'await Promise.all([page.waitForNavigation(), submitButton.click()]);' : 'await submitButton.click();'}\n              submitResult = { success: true, message: 'Form submitted successfully' };\n            } else {\n              submitResult = { success: false, error: 'Submit button not found' };\n            }\n          } catch (error) {\n            submitResult = { success: false, error: error.message };\n          }\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            formFillResults: results,\n            submitResult: submitResult,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * CAPTCHA solving with multiple strategies\n   */ async solveCaptcha(url, captchaType = 'recaptcha') {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const captchaType = \"${captchaType}\";\n        let result = { success: false, type: captchaType };\n\n        try {\n          if (captchaType === 'recaptcha') {\n            // Look for reCAPTCHA\n            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');\n            if (recaptcha) {\n              // For now, we'll detect and report the presence\n              // In production, integrate with 2captcha or similar service\n              const sitekey = await recaptcha.getAttribute('data-sitekey');\n              result = {\n                success: false,\n                type: 'recaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'reCAPTCHA detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'hcaptcha') {\n            // Look for hCaptcha\n            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');\n            if (hcaptcha) {\n              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');\n              result = {\n                success: false,\n                type: 'hcaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'hCaptcha detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'text') {\n            // Look for text-based CAPTCHA\n            const textCaptcha = await page.$('img[src*=\"captcha\"], img[alt*=\"captcha\"], .captcha-image');\n            if (textCaptcha) {\n              result = {\n                success: false,\n                type: 'text',\n                detected: true,\n                message: 'Text CAPTCHA detected but solving not implemented yet'\n              };\n            }\n          }\n\n          // If no CAPTCHA detected\n          if (!result.detected) {\n            result = {\n              success: true,\n              type: captchaType,\n              detected: false,\n              message: 'No CAPTCHA detected on page'\n            };\n          }\n        } catch (error) {\n          result = {\n            success: false,\n            type: captchaType,\n            error: error.message\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            captchaResult: result,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Execute custom JavaScript with advanced capabilities\n   */ async executeAdvancedScript(url, script, options) {\n        const waitForSelector = options?.waitForSelector || '';\n        const timeout = options?.timeout || 30000;\n        const returnType = options?.returnType || 'json';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        ${waitForSelector ? `await page.waitForSelector(\"${waitForSelector}\", { timeout: ${timeout} });` : ''}\n\n        // Execute custom script\n        const scriptResult = await page.evaluate(() => {\n          ${script}\n        });\n\n        let finalResult = scriptResult;\n\n        if (\"${returnType}\" === 'screenshot') {\n          const screenshot = await page.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n          finalResult = {\n            scriptResult: scriptResult,\n            screenshot: screenshot\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            result: finalResult,\n            returnType: \"${returnType}\",\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Smart content extraction with multiple strategies\n   */ async smartExtract(url, extractionGoals) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const goals = ${JSON.stringify(extractionGoals)};\n        const results = {};\n\n        // Common extraction patterns\n        const extractors = {\n          prices: () => {\n            const priceSelectors = [\n              '[class*=\"price\"]', '[id*=\"price\"]', '.cost', '.amount',\n              '[data-testid*=\"price\"]', '.currency', '[class*=\"dollar\"]'\n            ];\n            const prices = [];\n            priceSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const text = el.textContent?.trim();\n                if (text && /[$£€¥₹]|\\\\d+\\\\.\\\\d{2}/.test(text)) {\n                  prices.push({\n                    text: text,\n                    selector: selector,\n                    element: el.tagName\n                  });\n                }\n              });\n            });\n            return prices;\n          },\n\n          contact: () => {\n            const contactSelectors = [\n              '[href^=\"mailto:\"]', '[href^=\"tel:\"]', '.contact', '.email', '.phone'\n            ];\n            const contacts = [];\n            contactSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                contacts.push({\n                  text: el.textContent?.trim(),\n                  href: el.getAttribute('href'),\n                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'\n                });\n              });\n            });\n            return contacts;\n          },\n\n          products: () => {\n            const productSelectors = [\n              '.product', '[class*=\"product\"]', '.item', '[data-testid*=\"product\"]'\n            ];\n            const products = [];\n            productSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const title = el.querySelector('h1, h2, h3, .title, [class*=\"title\"]')?.textContent?.trim();\n                const price = el.querySelector('[class*=\"price\"], .cost')?.textContent?.trim();\n                const image = el.querySelector('img')?.src;\n                if (title) {\n                  products.push({ title, price, image });\n                }\n              });\n            });\n            return products;\n          },\n\n          text: () => {\n            // Extract main content\n            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];\n            let content = '';\n            for (const selector of contentSelectors) {\n              const el = document.querySelector(selector);\n              if (el) {\n                content = el.textContent?.trim() || '';\n                break;\n              }\n            }\n            if (!content) {\n              content = document.body.textContent?.trim() || '';\n            }\n            return content.substring(0, 5000); // Limit to 5000 chars\n          },\n\n          links: () => {\n            const links = [];\n            document.querySelectorAll('a[href]').forEach(el => {\n              const href = el.getAttribute('href');\n              const text = el.textContent?.trim();\n              if (href && text && !href.startsWith('#')) {\n                links.push({\n                  url: new URL(href, window.location.href).href,\n                  text: text\n                });\n              }\n            });\n            return links.slice(0, 50); // Limit to 50 links\n          }\n        };\n\n        // Execute extractors based on goals\n        goals.forEach(goal => {\n          const goalLower = goal.toLowerCase();\n          if (goalLower.includes('price') || goalLower.includes('cost')) {\n            results.prices = extractors.prices();\n          }\n          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {\n            results.contact = extractors.contact();\n          }\n          if (goalLower.includes('product') || goalLower.includes('item')) {\n            results.products = extractors.products();\n          }\n          if (goalLower.includes('text') || goalLower.includes('content')) {\n            results.text = extractors.text();\n          }\n          if (goalLower.includes('link') || goalLower.includes('url')) {\n            results.links = extractors.links();\n          }\n        });\n\n        // If no specific goals, extract everything\n        if (goals.length === 0) {\n          Object.keys(extractors).forEach(key => {\n            results[key] = extractors[key]();\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            extractionGoals: goals,\n            results: results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    // Get service statistics\n    getStats() {\n        return {\n            totalKeys: this.apiKeys.length,\n            healthyKeys: this.getHealthyKeys().length,\n            keyUsage: Object.fromEntries(this.keyUsageCount),\n            keyErrors: Object.fromEntries(this.keyErrors)\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowserlessService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browserless.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fweb-browsing%2Froute&page=%2Fapi%2Ftools%2Fweb-browsing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fweb-browsing%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();