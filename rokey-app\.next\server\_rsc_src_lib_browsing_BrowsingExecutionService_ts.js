"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_browsing_BrowsingExecutionService_ts";
exports.ids = ["_rsc_src_lib_browsing_BrowsingExecutionService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/browserless.ts":
/*!********************************!*\
  !*** ./src/lib/browserless.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Browserless.io API service with key rotation\n// Similar to Jina API key rotation system\nclass BrowserlessService {\n    constructor(){\n        this.apiKeys = [];\n        this.currentKeyIndex = 0;\n        this.keyUsageCount = new Map();\n        this.keyErrors = new Map();\n        this.MAX_RETRIES = 3;\n        this.ERROR_THRESHOLD = 5;\n        this.ENDPOINT = 'https://production-sfo.browserless.io';\n        this.initializeKeys();\n        this.testConnectivity();\n    }\n    static getInstance() {\n        if (!BrowserlessService.instance) {\n            BrowserlessService.instance = new BrowserlessService();\n        }\n        return BrowserlessService.instance;\n    }\n    initializeKeys() {\n        // Load all Browserless API keys from environment (similar to Jina pattern)\n        this.apiKeys = [\n            process.env.BROWSERLESS_API_KEY,\n            process.env.BROWSERLESS_API_KEY_2,\n            process.env.BROWSERLESS_API_KEY_3,\n            process.env.BROWSERLESS_API_KEY_4,\n            process.env.BROWSERLESS_API_KEY_5,\n            process.env.BROWSERLESS_API_KEY_6,\n            process.env.BROWSERLESS_API_KEY_7,\n            process.env.BROWSERLESS_API_KEY_8,\n            process.env.BROWSERLESS_API_KEY_9,\n            process.env.BROWSERLESS_API_KEY_10\n        ].filter(Boolean);\n        if (this.apiKeys.length === 0) {\n            console.error('No Browserless API keys found in environment variables');\n            return;\n        }\n        console.log(`[Browserless] Initialized with ${this.apiKeys.length} API keys`);\n        // Initialize usage tracking\n        this.apiKeys.forEach((key)=>{\n            this.keyUsageCount.set(key, 0);\n            this.keyErrors.set(key, 0);\n        });\n    }\n    /**\n   * Test connectivity to Browserless service\n   */ async testConnectivity() {\n        if (this.apiKeys.length === 0) {\n            console.warn('[Browserless] No API keys available for connectivity test');\n            return;\n        }\n        try {\n            const testKey = this.apiKeys[0];\n            const testUrl = `${this.ENDPOINT}/function?token=${testKey}`;\n            // Simple connectivity test with proper function format\n            const testCode = `\n        export default async function ({ page }) {\n          return {\n            status: \"connectivity-test-success\",\n            timestamp: new Date().toISOString()\n          };\n        }\n      `;\n            const response = await fetch(testUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/javascript'\n                },\n                body: testCode,\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            if (response.ok) {\n                const result = await response.text();\n                console.log('[Browserless] ✅ Connectivity test successful:', result);\n            } else {\n                const errorText = await response.text();\n                console.warn(`[Browserless] ⚠️ Connectivity test failed with status: ${response.status}`);\n                console.warn(`[Browserless] Error details: ${errorText}`);\n                console.warn(`[Browserless] Using key: ${testKey.substring(0, 8)}...`);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.warn(`[Browserless] ⚠️ Connectivity test failed: ${errorMessage}`);\n            // Check for specific network errors\n            if (errorMessage.includes('ENOTFOUND')) {\n                console.error('[Browserless] 🌐 DNS resolution failed - check if chrome.browserless.io is accessible');\n            } else if (errorMessage.includes('ECONNRESET')) {\n                console.error('[Browserless] 🔌 Connection reset - possible network or service issue');\n            }\n        }\n    }\n    getNextApiKey() {\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Browserless API keys available');\n        }\n        // Find the key with lowest usage and errors\n        let bestKey = this.apiKeys[0];\n        let bestScore = this.calculateKeyScore(bestKey);\n        for (const key of this.apiKeys){\n            const score = this.calculateKeyScore(key);\n            if (score < bestScore) {\n                bestKey = key;\n                bestScore = score;\n            }\n        }\n        return bestKey;\n    }\n    calculateKeyScore(key) {\n        const usage = this.keyUsageCount.get(key) || 0;\n        const errors = this.keyErrors.get(key) || 0;\n        // Higher score = worse key (more usage + more errors)\n        return usage + errors * 10;\n    }\n    incrementKeyUsage(key) {\n        const currentUsage = this.keyUsageCount.get(key) || 0;\n        this.keyUsageCount.set(key, currentUsage + 1);\n    }\n    incrementKeyError(key) {\n        const currentErrors = this.keyErrors.get(key) || 0;\n        this.keyErrors.set(key, currentErrors + 1);\n    }\n    isKeyHealthy(key) {\n        const errors = this.keyErrors.get(key) || 0;\n        return errors < this.ERROR_THRESHOLD;\n    }\n    getHealthyKeys() {\n        return this.apiKeys.filter((key)=>this.isKeyHealthy(key));\n    }\n    async executeFunction(code, context, config) {\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            // Reset error counts if all keys are unhealthy\n            this.keyErrors.clear();\n            this.apiKeys.forEach((key)=>this.keyErrors.set(key, 0));\n            console.log('All Browserless keys were unhealthy, resetting error counts');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const response = await this.makeRequest(apiKey, code, context, config);\n                // Success - return the response\n                return response;\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless attempt ${attempt + 1} failed:`, error);\n                // If it's a rate limit or quota error, mark the key as having an error\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless API attempts failed');\n    }\n    async makeRequest(apiKey, code, context, config) {\n        const url = `${this.ENDPOINT}/function?token=${apiKey}`;\n        const requestBody = context ? {\n            code,\n            context\n        } : code;\n        const headers = {\n            'Content-Type': context ? 'application/json' : 'application/javascript',\n            'User-Agent': config?.userAgent || 'RouKey-Browser-Agent/1.0'\n        };\n        const response = await fetch(url, {\n            method: 'POST',\n            headers,\n            body: context ? JSON.stringify(requestBody) : code,\n            signal: AbortSignal.timeout(config?.timeout || 30000)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Browserless API error: ${response.status} - ${errorText}`);\n        }\n        const result = await response.json();\n        return result;\n    }\n    isRateLimitError(error) {\n        const message = error.message.toLowerCase();\n        return message.includes('rate limit') || message.includes('quota') || message.includes('429') || message.includes('too many requests');\n    }\n    // Convenience methods for common browser tasks\n    async navigateAndExtract(url, selector) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n        \n        const title = await page.title();\n        const content = ${selector ? `await page.$eval(\"${selector}\", el => el.textContent || el.innerText)` : 'await page.evaluate(() => document.body.innerText)'};\n        \n        return {\n          data: {\n            url: \"${url}\",\n            title,\n            content: content?.trim() || \"\"\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced BrowserQL automation for complex browsing tasks\n   * Supports form filling, CAPTCHA solving, multi-step workflows, and state-of-the-art browsing\n   */ async executeBrowserQLAutomation(automationScript, options = {}) {\n        const { timeout = 60000, humanLike = true, solveCaptcha = true, sessionId, screenshots = false } = options;\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            throw new Error('No healthy Browserless API keys available');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.maxRetries; attempt++){\n            try {\n                const currentKey = this.getNextApiKey();\n                console.log(`[Browserless] BrowserQL automation attempt ${attempt + 1} with key: ${currentKey.name}`);\n                console.log(`[Browserless] Script length: ${automationScript.length} characters`);\n                // Build the endpoint URL\n                let endpoint = `https://production-sfo.browserless.io/chromium/bql?token=${currentKey.key}`;\n                if (sessionId) {\n                    endpoint += `&sessionId=${sessionId}`;\n                }\n                const requestBody = {\n                    query: automationScript,\n                    variables: {\n                        timeout,\n                        humanLike,\n                        solveCaptcha,\n                        screenshots\n                    }\n                };\n                console.log(`[Browserless] Request body:`, JSON.stringify(requestBody, null, 2));\n                const response = await fetch(endpoint, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'User-Agent': 'RouKey-Browser-Automation/1.0'\n                    },\n                    body: JSON.stringify(requestBody),\n                    signal: AbortSignal.timeout(timeout + 10000) // Add buffer to timeout\n                });\n                console.log(`[Browserless] Response status: ${response.status} ${response.statusText}`);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    console.error(`[Browserless] Error response body:`, errorText);\n                    throw new Error(`BrowserQL automation failed: ${response.status} ${response.statusText} - ${errorText}`);\n                }\n                const result = await response.json();\n                console.log(`[Browserless] Response result:`, JSON.stringify(result, null, 2));\n                if (result.errors && result.errors.length > 0) {\n                    console.error(`[Browserless] BrowserQL errors:`, result.errors);\n                    throw new Error(`BrowserQL errors: ${result.errors.map((e)=>e.message).join(', ')}`);\n                }\n                console.log(`[Browserless] ✅ BrowserQL automation successful`);\n                return {\n                    data: result.data,\n                    type: \"application/json\",\n                    sessionId: result.sessionId,\n                    screenshots: result.screenshots\n                };\n            } catch (error) {\n                lastError = error;\n                console.error(`[Browserless] BrowserQL automation attempt ${attempt + 1} failed:`, error);\n                console.error(`[Browserless] Error details:`, {\n                    message: error.message,\n                    stack: error.stack,\n                    name: error.name\n                });\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All BrowserQL automation attempts failed');\n    }\n    /**\n   * Create a persistent browsing session for complex multi-step workflows\n   */ async createBrowsingSession(initialUrl, options = {}) {\n        const { timeout = 300000, humanLike = true, blockResources = [\n            '*.png',\n            '*.jpg',\n            '*.gif',\n            '*.mp4',\n            '*.css'\n        ] } = options;\n        const sessionScript = `\n      mutation CreateBrowsingSession {\n        ${blockResources.length > 0 ? `\n        reject(patterns: ${JSON.stringify(blockResources)})\n        ` : ''}\n\n        goto(url: \"${initialUrl}\", waitUntil: networkIdle) {\n          status\n          time\n        }\n\n        ${humanLike ? `\n        # Add human-like behavior\n        waitForTimeout(time: ${Math.floor(Math.random() * 2000) + 1000}) {\n          time\n        }\n        ` : ''}\n\n        reconnect(timeout: ${timeout}) {\n          BrowserQLEndpoint\n        }\n      }\n    `;\n        const result = await this.executeBrowserQLAutomation(sessionScript, {\n            timeout\n        });\n        return {\n            sessionId: result.sessionId || 'default',\n            reconnectUrl: result.data.reconnect.BrowserQLEndpoint\n        };\n    }\n    /**\n   * Advanced form filling with human-like behavior\n   */ async fillFormAdvanced(sessionUrl, formData, submitSelector) {\n        const formScript = `\n      mutation FillFormAdvanced {\n        ${formData.map((field, index)=>{\n            const delay = field.delay || [\n                50,\n                150\n            ];\n            return `\n            field${index}: ${field.type === 'select' ? 'select' : field.type === 'checkbox' || field.type === 'radio' ? 'click' : 'type'}(\n              selector: \"${field.selector}\"\n              ${field.type !== 'checkbox' && field.type !== 'radio' ? `text: \"${field.value}\"` : ''}\n              ${field.type === 'text' || field.type === 'email' || field.type === 'password' ? `delay: [${delay[0]}, ${delay[1]}]` : ''}\n              visible: true\n            ) {\n              time\n              ${field.type === 'text' || field.type === 'email' || field.type === 'password' ? 'text' : 'x'}\n            }\n          `;\n        }).join('\\n')}\n\n        ${submitSelector ? `\n        submitForm: click(\n          selector: \"${submitSelector}\"\n          visible: true\n        ) {\n          time\n          x\n          y\n        }\n        ` : ''}\n\n        # Wait for any page changes after form submission\n        waitForTimeout(time: 2000) {\n          time\n        }\n      }\n    `;\n        return await this.executeBrowserQLWithSession(sessionUrl, formScript);\n    }\n    /**\n   * Execute BrowserQL script with existing session\n   */ async executeBrowserQLWithSession(sessionUrl, script) {\n        try {\n            const response = await fetch(sessionUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: script\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`Session execution failed: ${response.status} ${response.statusText}`);\n            }\n            const result = await response.json();\n            if (result.errors && result.errors.length > 0) {\n                throw new Error(`Session errors: ${result.errors.map((e)=>e.message).join(', ')}`);\n            }\n            return result;\n        } catch (error) {\n            console.error('[Browserless] Session execution failed:', error);\n            throw error;\n        }\n    }\n    async searchAndExtractUnblocked(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            throw new Error('No healthy Browserless API keys available');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const url = `${this.ENDPOINT}/unblock?token=${apiKey}`;\n                const requestBody = {\n                    url: searchUrl,\n                    content: true,\n                    browserWSEndpoint: false,\n                    cookies: false,\n                    screenshot: false,\n                    waitForSelector: {\n                        selector: 'h3, .g h3, .LC20lb, .b_algo h2',\n                        timeout: 10000\n                    }\n                };\n                const response = await fetch(url, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'User-Agent': 'RouKey-Browser-Agent/1.0'\n                    },\n                    body: JSON.stringify(requestBody),\n                    signal: AbortSignal.timeout(60000)\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`Browserless unblock API error: ${response.status} - ${errorText}`);\n                }\n                const result = await response.json();\n                if (result.content) {\n                    // Parse the HTML content to extract search results\n                    const searchResults = this.parseSearchResults(result.content, searchEngine, query);\n                    return {\n                        data: {\n                            query,\n                            searchEngine,\n                            results: searchResults,\n                            timestamp: new Date().toISOString(),\n                            debug: {\n                                pageTitle: 'Unblocked search',\n                                pageUrl: searchUrl,\n                                totalElements: searchResults.length,\n                                usedSelector: 'unblock-api',\n                                extractedCount: searchResults.length\n                            }\n                        },\n                        type: \"application/json\"\n                    };\n                } else {\n                    throw new Error('No content returned from unblock API');\n                }\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless unblock attempt ${attempt + 1} failed:`, error);\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless unblock API attempts failed');\n    }\n    /**\n   * Handle infinite scroll and pagination automatically\n   */ async handleInfiniteScroll(sessionUrl, options = {}) {\n        const { maxScrolls = 10, scrollDelay = 2000, contentSelector = 'body', stopCondition = 'no-more-content' } = options;\n        const scrollScript = `\n      mutation HandleInfiniteScroll {\n        # Get initial content count\n        initialContent: text(selector: \"${contentSelector}\") {\n          text\n        }\n\n        # Perform scrolling with content monitoring\n        ${Array.from({\n            length: maxScrolls\n        }, (_, i)=>`\n          scroll${i}: scroll(\n            direction: down\n            distance: 1000\n          ) {\n            time\n          }\n\n          waitAfterScroll${i}: waitForTimeout(time: ${scrollDelay}) {\n            time\n          }\n\n          contentCheck${i}: text(selector: \"${contentSelector}\") {\n            text\n          }\n        `).join('\\n')}\n\n        # Get final content\n        finalContent: text(selector: \"${contentSelector}\") {\n          text\n        }\n      }\n    `;\n        return await this.executeBrowserQLWithSession(sessionUrl, scrollScript);\n    }\n    /**\n   * Advanced content extraction with multiple strategies\n   */ async extractContentAdvanced(sessionUrl, extractionRules) {\n        const extractionScript = `\n      mutation ExtractContentAdvanced {\n        ${extractionRules.map((rule, index)=>{\n            if (rule.type === 'screenshot') {\n                return `\n              ${rule.name}: screenshot(\n                selector: \"${rule.selector}\"\n                fullPage: false\n              ) {\n                data\n              }\n            `;\n            } else if (rule.type === 'attribute') {\n                return `\n              ${rule.name}: html(\n                selector: \"${rule.selector}\"\n                visible: true\n              ) {\n                html\n              }\n            `;\n            } else {\n                return `\n              ${rule.name}: ${rule.type}(\n                selector: \"${rule.selector}\"\n                visible: true\n              ) {\n                ${rule.type}\n              }\n            `;\n            }\n        }).join('\\n')}\n      }\n    `;\n        return await this.executeBrowserQLWithSession(sessionUrl, extractionScript);\n    }\n    /**\n   * Complex multi-step automation workflow\n   */ async executeComplexWorkflow(initialUrl, workflow) {\n        // Create session first\n        const session = await this.createBrowsingSession(initialUrl, {\n            timeout: 600000,\n            humanLike: true\n        });\n        const workflowScript = `\n      mutation ComplexWorkflow {\n        ${workflow.map((step, index)=>{\n            switch(step.type){\n                case 'navigate':\n                    return `\n                step${index}_navigate: goto(\n                  url: \"${step.params.url}\"\n                  waitUntil: ${step.params.waitUntil || 'networkIdle'}\n                ) {\n                  status\n                  time\n                }\n              `;\n                case 'click':\n                    // Escape quotes in selector and simplify for BrowserQL\n                    const clickSelector = step.params.selector.replace(/\"/g, '\\\\\"');\n                    const clickTimeout = step.params.timeout || 5000;\n                    return `\n                step${index}_click: click(\n                  selector: \"${clickSelector}\"\n                  visible: true\n                  timeout: ${clickTimeout}\n                  scroll: true\n                ) {\n                  time\n                  x\n                  y\n                }\n              `;\n                case 'type':\n                    // Escape quotes in selector and text for BrowserQL\n                    const typeSelector = step.params.selector.replace(/\"/g, '\\\\\"');\n                    const typeText = step.params.text.replace(/\"/g, '\\\\\"');\n                    const typeTimeout = step.params.timeout || 5000;\n                    return `\n                step${index}_type: type(\n                  selector: \"${typeSelector}\"\n                  text: \"${typeText}\"\n                  visible: true\n                  timeout: ${typeTimeout}\n                  ${step.params.clear ? 'clear: true' : ''}\n                ) {\n                  time\n                  text\n                }\n              `;\n                case 'wait':\n                    return `\n                step${index}_wait: waitForTimeout(time: ${step.params.time || 1000}) {\n                  time\n                }\n              `;\n                case 'waitForSelector':\n                    const waitSelector = step.params.selector.replace(/\"/g, '\\\\\"');\n                    const waitTimeout = step.params.timeout || 10000;\n                    return `\n                step${index}_waitForSelector: waitForSelector(\n                  selector: \"${waitSelector}\"\n                  timeout: ${waitTimeout}\n                  visible: true\n                ) {\n                  time\n                }\n              `;\n                case 'extract':\n                    const extractType = step.params.type || 'text';\n                    const extractSelector = step.params.selector.replace(/\"/g, '\\\\\"');\n                    if (extractType === 'html') {\n                        return `\n                  step${index}_extract: html(\n                    selector: \"${extractSelector}\"\n                    visible: true\n                  ) {\n                    html\n                  }\n                `;\n                    } else {\n                        return `\n                  step${index}_extract: text(\n                    selector: \"${extractSelector}\"\n                    visible: true\n                  ) {\n                    text\n                  }\n                `;\n                    }\n                case 'scroll':\n                    return `\n                step${index}_scroll: scroll(\n                  selector: \"${step.params.selector || 'body'}\"\n                  ${step.params.distance ? `distance: ${step.params.distance}` : ''}\n                ) {\n                  time\n                }\n              `;\n                case 'captcha':\n                    return `\n                step${index}_captcha: solve(\n                  type: ${step.params.type || 'hcaptcha'}\n                ) {\n                  solved\n                  time\n                }\n              `;\n                case 'screenshot':\n                    return `\n                step${index}_screenshot: screenshot(\n                  ${step.params.selector ? `selector: \"${step.params.selector}\"` : ''}\n                  fullPage: ${step.params.fullPage || false}\n                ) {\n                  data\n                }\n              `;\n                default:\n                    return `# Unknown step type: ${step.type}`;\n            }\n        }).join('\\n')}\n\n        # Final status check\n        finalStatus: html {\n          html\n        }\n      }\n    `;\n        try {\n            const result = await this.executeBrowserQLWithSession(session.reconnectUrl, workflowScript);\n            return {\n                ...result,\n                sessionId: session.sessionId,\n                reconnectUrl: session.reconnectUrl\n            };\n        } catch (error) {\n            console.error('[Browserless] Complex workflow failed:', error);\n            throw error;\n        }\n    }\n    parseSearchResults(htmlContent, searchEngine, query) {\n        // Modern search result parsing with comprehensive snippet extraction\n        // Updated for 2025 Google/Bing HTML structures to provide accurate, current information\n        const results = [];\n        if (searchEngine === 'google') {\n            // Updated Google search result patterns for 2025\n            // Google now uses more dynamic class names and data attributes\n            const modernResultPatterns = [\n                // Main search result containers (2025 patterns)\n                /<div[^>]*data-ved=\"[^\"]*\"[^>]*class=\"[^\"]*g[^\"]*\"[^>]*>(.*?)<\\/div>/gis,\n                /<div[^>]*class=\"[^\"]*MjjYud[^\"]*\"[^>]*>(.*?)<\\/div>/gis,\n                /<div[^>]*class=\"[^\"]*kvH3mc[^\"]*\"[^>]*>(.*?)<\\/div>/gis,\n                /<div[^>]*class=\"[^\"]*N54PNb[^\"]*\"[^>]*>(.*?)<\\/div>/gis\n            ];\n            // Modern title patterns\n            const titlePatterns = [\n                /<h3[^>]*class=\"[^\"]*LC20lb[^\"]*\"[^>]*>([^<]+)<\\/h3>/i,\n                /<h3[^>]*class=\"[^\"]*DKV0Md[^\"]*\"[^>]*>([^<]+)<\\/h3>/i,\n                /<h3[^>]*>([^<]+)<\\/h3>/i\n            ];\n            // Modern link patterns\n            const linkPatterns = [\n                /<a[^>]*href=\"([^\"]*)\"[^>]*data-ved=\"[^\"]*\"[^>]*>/i,\n                /<a[^>]*href=\"([^\"]*)\"[^>]*>/i\n            ];\n            // Updated snippet patterns for 2025\n            const snippetPatterns = [\n                // Current Google snippet classes\n                /<span[^>]*class=\"[^\"]*aCOpRe[^\"]*\"[^>]*>([^<]+)<\\/span>/i,\n                /<div[^>]*class=\"[^\"]*VwiC3b[^\"]*\"[^>]*>([^<]+)<\\/div>/i,\n                /<span[^>]*class=\"[^\"]*st[^\"]*\"[^>]*>([^<]+)<\\/span>/i,\n                /<div[^>]*data-content-feature=\"snippet\"[^>]*>([^<]+)<\\/div>/i,\n                /<span[^>]*data-snippet=\"[^\"]*\"[^>]*>([^<]+)<\\/span>/i,\n                // Fallback patterns\n                /<div[^>]*class=\"[^\"]*IsZvec[^\"]*\"[^>]*>([^<]+)<\\/div>/i,\n                /<span[^>]*class=\"[^\"]*hgKElc[^\"]*\"[^>]*>([^<]+)<\\/span>/i\n            ];\n            // Try each result container pattern\n            for (const containerPattern of modernResultPatterns){\n                let containerMatch;\n                while((containerMatch = containerPattern.exec(htmlContent)) !== null && results.length < 10){\n                    const containerContent = containerMatch[1];\n                    // Extract title using multiple patterns\n                    let title = '';\n                    for (const titlePattern of titlePatterns){\n                        const titleMatch = titlePattern.exec(containerContent);\n                        if (titleMatch) {\n                            title = titleMatch[1].trim();\n                            break;\n                        }\n                    }\n                    // Extract link using multiple patterns\n                    let link = '';\n                    for (const linkPattern of linkPatterns){\n                        const linkMatch = linkPattern.exec(containerContent);\n                        if (linkMatch) {\n                            link = linkMatch[1];\n                            break;\n                        }\n                    }\n                    if (title && link && link.startsWith('http') && !link.includes('google.com') && !link.includes('/search?') && !link.includes('webcache.googleusercontent.com')) {\n                        // Extract snippet using multiple patterns\n                        let snippet = '';\n                        for (const snippetPattern of snippetPatterns){\n                            const snippetMatch = snippetPattern.exec(containerContent);\n                            if (snippetMatch) {\n                                snippet = snippetMatch[1].trim();\n                                // Clean up HTML entities and normalize whitespace\n                                snippet = snippet.replace(/&[^;]+;/g, ' ').replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim();\n                                break;\n                            }\n                        }\n                        // Enhanced fallback snippet extraction\n                        if (!snippet) {\n                            // Try to extract any meaningful text from the container\n                            const cleanText = containerContent.replace(/<script[^>]*>.*?<\\/script>/gis, '').replace(/<style[^>]*>.*?<\\/style>/gis, '').replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim();\n                            const sentences = cleanText.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n                            if (sentences.length > 0) {\n                                snippet = sentences[0].trim().substring(0, 200) + '...';\n                            } else {\n                                const words = cleanText.split(' ').filter((w)=>w.length > 2);\n                                if (words.length > 10) {\n                                    snippet = words.slice(0, 25).join(' ') + '...';\n                                }\n                            }\n                        }\n                        results.push({\n                            title,\n                            link,\n                            snippet: snippet || 'No description available',\n                            searchEngine: 'google',\n                            query,\n                            timestamp: new Date().toISOString(),\n                            relevanceScore: snippet.length > 50 ? 0.9 : 0.7 // Higher score for better snippets\n                        });\n                    }\n                }\n                if (results.length > 0) break; // Stop if we found results with this pattern\n            }\n        } else {\n            // Updated Bing search result patterns for 2025\n            const bingContainerPatterns = [\n                /<li[^>]*class=\"[^\"]*b_algo[^\"]*\"[^>]*>(.*?)<\\/li>/gis,\n                /<div[^>]*class=\"[^\"]*b_algoheader[^\"]*\"[^>]*>(.*?)<\\/div>/gis\n            ];\n            const bingTitlePatterns = [\n                /<h2[^>]*><a[^>]*href=\"([^\"]*)\"[^>]*>([^<]+)<\\/a><\\/h2>/i,\n                /<a[^>]*href=\"([^\"]*)\"[^>]*><h2[^>]*>([^<]+)<\\/h2><\\/a>/i\n            ];\n            const bingSnippetPatterns = [\n                /<p[^>]*class=\"[^\"]*b_lineclamp[^\"]*\"[^>]*>([^<]+)<\\/p>/i,\n                /<div[^>]*class=\"[^\"]*b_caption[^\"]*\"[^>]*>([^<]+)<\\/div>/i,\n                /<p[^>]*>([^<]+)<\\/p>/i\n            ];\n            for (const containerPattern of bingContainerPatterns){\n                let containerMatch;\n                while((containerMatch = containerPattern.exec(htmlContent)) !== null && results.length < 10){\n                    const containerContent = containerMatch[1];\n                    let title = '', link = '';\n                    for (const titlePattern of bingTitlePatterns){\n                        const titleMatch = titlePattern.exec(containerContent);\n                        if (titleMatch) {\n                            link = titleMatch[1];\n                            title = titleMatch[2].trim();\n                            break;\n                        }\n                    }\n                    if (title && link) {\n                        let snippet = '';\n                        for (const snippetPattern of bingSnippetPatterns){\n                            const snippetMatch = snippetPattern.exec(containerContent);\n                            if (snippetMatch) {\n                                snippet = snippetMatch[1].trim().replace(/&[^;]+;/g, ' ').replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim();\n                                break;\n                            }\n                        }\n                        results.push({\n                            title,\n                            link,\n                            snippet: snippet || 'No description available',\n                            searchEngine: 'bing',\n                            query,\n                            timestamp: new Date().toISOString(),\n                            relevanceScore: snippet.length > 50 ? 0.9 : 0.7\n                        });\n                    }\n                }\n                if (results.length > 0) break;\n            }\n        }\n        // Sort results by relevance score (better snippets first)\n        results.sort((a, b)=>(b.relevanceScore || 0) - (a.relevanceScore || 0));\n        console.log(`[Browserless] Parsed ${results.length} search results with enhanced snippets for query: \"${query}\"`);\n        return results.slice(0, 8); // Return top 8 most relevant results\n    }\n    async searchAndExtract(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const code = `\n      export default async function ({ page }) {\n        // Set a realistic user agent and headers\n        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');\n        await page.setExtraHTTPHeaders({\n          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n          'Accept-Language': 'en-US,en;q=0.5',\n          'Accept-Encoding': 'gzip, deflate',\n          'DNT': '1',\n          'Connection': 'keep-alive',\n          'Upgrade-Insecure-Requests': '1',\n        });\n\n        await page.goto(\"${searchUrl}\", { waitUntil: 'networkidle0' });\n\n        // Set up CAPTCHA solving\n        const cdp = await page.createCDPSession();\n\n        // Check for CAPTCHA and solve if found\n        let captchaFound = false;\n        cdp.on('Browserless.captchaFound', () => {\n          console.log('CAPTCHA detected on search page');\n          captchaFound = true;\n        });\n\n        // Wait a moment to see if CAPTCHA is detected\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        if (captchaFound) {\n          console.log('Attempting to solve CAPTCHA...');\n          try {\n            const { solved, error } = await cdp.send('Browserless.solveCaptcha');\n            console.log('CAPTCHA solving result:', { solved, error });\n\n            if (solved) {\n              console.log('CAPTCHA solved successfully');\n              // Wait for page to reload after CAPTCHA solving\n              await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 10000 }).catch(() => {\n                console.log('No navigation after CAPTCHA solve, continuing...');\n              });\n            } else {\n              console.log('CAPTCHA solving failed:', error);\n            }\n          } catch (captchaError) {\n            console.log('CAPTCHA solving error:', captchaError);\n          }\n        }\n\n        // Wait for search results to load with multiple fallback selectors\n        let resultsLoaded = false;\n        const googleSelectors = ['[data-ved]', 'h3', '.g h3', '.LC20lb', '.DKV0Md', '#search h3'];\n        const bingSelectors = ['.b_algo', '.b_algo h2', 'h2 a'];\n\n        const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n\n        for (const selector of selectorsToTry) {\n          try {\n            await page.waitForSelector(selector, { timeout: 3000 });\n            resultsLoaded = true;\n            console.log('Found results with selector:', selector);\n            break;\n          } catch (e) {\n            console.log('Selector failed:', selector);\n            continue;\n          }\n        }\n\n        if (!resultsLoaded) {\n          // Give it one more chance with a longer timeout on the most common selector\n          try {\n            await page.waitForSelector('${searchEngine === 'google' ? 'h3' : '.b_algo'}', { timeout: 5000 });\n          } catch (e) {\n            console.log('All selectors failed, proceeding anyway...');\n          }\n        }\n\n        const results = await page.evaluate(() => {\n          console.log('Starting search results extraction...');\n\n          // Try multiple selectors for extracting results\n          const googleSelectors = [\n            '[data-ved] h3',\n            'h3',\n            '.g h3',\n            '.LC20lb',\n            '.DKV0Md',\n            '#search h3',\n            '.yuRUbf h3',\n            'a h3',\n            '[role=\"heading\"]'\n          ];\n          const bingSelectors = ['.b_algo h2', '.b_algo h2 a', 'h2 a'];\n\n          const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n          let elements = [];\n          let usedSelector = '';\n\n          // Debug: Log page content\n          console.log('Page title:', document.title);\n          console.log('Page URL:', window.location.href);\n          console.log('Page body preview:', document.body.innerText.substring(0, 500));\n\n          for (const selector of selectorsToTry) {\n            elements = document.querySelectorAll(selector);\n            console.log('Trying selector:', selector, 'found:', elements.length, 'elements');\n            if (elements.length > 0) {\n              usedSelector = selector;\n              console.log('Found', elements.length, 'results with selector:', selector);\n              break;\n            }\n          }\n\n          // If no results found, try a more generic approach\n          if (elements.length === 0) {\n            console.log('No results with specific selectors, trying generic approach...');\n            // Try to find any links that look like search results\n            const allLinks = document.querySelectorAll('a[href*=\"/url?\"]');\n            console.log('Found', allLinks.length, 'Google result links');\n\n            if (allLinks.length > 0) {\n              elements = Array.from(allLinks).map(link => {\n                const h3 = link.querySelector('h3');\n                return h3 || link;\n              }).filter(el => el && el.textContent?.trim());\n              usedSelector = 'a[href*=\"/url?\"] h3 (fallback)';\n              console.log('Using fallback approach, found', elements.length, 'elements');\n            }\n          }\n\n          const extractedResults = Array.from(elements).slice(0, 5).map(el => {\n            const title = el.textContent?.trim() || '';\n            let link = '';\n\n            // Try to get the link\n            if (el.href) {\n              link = el.href;\n            } else {\n              const closestLink = el.closest('a');\n              if (closestLink) {\n                link = closestLink.href;\n              }\n            }\n\n            return { title, link };\n          }).filter(item => item.title && item.link);\n\n          console.log('Final results:', extractedResults.length, 'items using selector:', usedSelector);\n\n          return {\n            results: extractedResults,\n            debug: {\n              pageTitle: document.title,\n              pageUrl: window.location.href,\n              totalElements: elements.length,\n              usedSelector: usedSelector || 'none',\n              extractedCount: extractedResults.length\n            }\n          };\n        });\n\n        return {\n          data: {\n            query: \"${query}\",\n            searchEngine: \"${searchEngine}\",\n            results: results.results,\n            timestamp: new Date().toISOString(),\n            debug: results.debug\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code, null, {\n            timeout: 60000 // Increase timeout for CAPTCHA solving\n        });\n    }\n    async takeScreenshot(url, options) {\n        const fullPage = options?.fullPage ?? false;\n        const selector = options?.selector || '';\n        const quality = options?.quality || 80;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        let screenshot;\n        if (\"${selector}\") {\n          // Screenshot specific element\n          const element = await page.waitForSelector(\"${selector}\", { timeout: 10000 });\n          screenshot = await element.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n        } else {\n          // Screenshot full page or viewport\n          screenshot = await page.screenshot({\n            encoding: 'base64',\n            fullPage: ${fullPage},\n            type: 'png',\n            quality: ${quality}\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            screenshot: screenshot,\n            selector: \"${selector}\",\n            fullPage: ${fullPage},\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced form filling with intelligent field detection\n   */ async fillForm(url, formData, options) {\n        const submitAfterFill = options?.submitAfterFill ?? false;\n        const waitForNavigation = options?.waitForNavigation ?? false;\n        const formSelector = options?.formSelector || 'form';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const formData = ${JSON.stringify(formData)};\n        const results = [];\n\n        // Wait for form to be present\n        await page.waitForSelector(\"${formSelector}\", { timeout: 10000 });\n\n        // Fill each field intelligently\n        for (const [fieldName, value] of Object.entries(formData)) {\n          try {\n            // Try multiple selector strategies\n            const selectors = [\n              \\`input[name=\"\\${fieldName}\"]\\`,\n              \\`input[id=\"\\${fieldName}\"]\\`,\n              \\`textarea[name=\"\\${fieldName}\"]\\`,\n              \\`select[name=\"\\${fieldName}\"]\\`,\n              \\`input[placeholder*=\"\\${fieldName}\"]\\`,\n              \\`input[aria-label*=\"\\${fieldName}\"]\\`,\n              \\`[data-testid=\"\\${fieldName}\"]\\`\n            ];\n\n            let filled = false;\n            for (const selector of selectors) {\n              const elements = await page.$$(selector);\n              if (elements.length > 0) {\n                const element = elements[0];\n                const tagName = await element.evaluate(el => el.tagName.toLowerCase());\n\n                if (tagName === 'select') {\n                  await element.selectOption(value.toString());\n                } else if (tagName === 'input') {\n                  const inputType = await element.getAttribute('type');\n                  if (inputType === 'checkbox' || inputType === 'radio') {\n                    if (value) await element.check();\n                  } else {\n                    await element.fill(value.toString());\n                  }\n                } else {\n                  await element.fill(value.toString());\n                }\n\n                results.push({\n                  field: fieldName,\n                  selector: selector,\n                  value: value,\n                  success: true\n                });\n                filled = true;\n                break;\n              }\n            }\n\n            if (!filled) {\n              results.push({\n                field: fieldName,\n                value: value,\n                success: false,\n                error: 'Field not found'\n              });\n            }\n          } catch (error) {\n            results.push({\n              field: fieldName,\n              value: value,\n              success: false,\n              error: error.message\n            });\n          }\n        }\n\n        let submitResult = null;\n        if (${submitAfterFill}) {\n          try {\n            const submitButton = await page.$('input[type=\"submit\"], button[type=\"submit\"], button:has-text(\"Submit\")');\n            if (submitButton) {\n              ${waitForNavigation ? 'await Promise.all([page.waitForNavigation(), submitButton.click()]);' : 'await submitButton.click();'}\n              submitResult = { success: true, message: 'Form submitted successfully' };\n            } else {\n              submitResult = { success: false, error: 'Submit button not found' };\n            }\n          } catch (error) {\n            submitResult = { success: false, error: error.message };\n          }\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            formFillResults: results,\n            submitResult: submitResult,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * CAPTCHA solving with multiple strategies\n   */ async solveCaptcha(url, captchaType = 'recaptcha') {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const captchaType = \"${captchaType}\";\n        let result = { success: false, type: captchaType };\n\n        try {\n          if (captchaType === 'recaptcha') {\n            // Look for reCAPTCHA\n            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');\n            if (recaptcha) {\n              // For now, we'll detect and report the presence\n              // In production, integrate with 2captcha or similar service\n              const sitekey = await recaptcha.getAttribute('data-sitekey');\n              result = {\n                success: false,\n                type: 'recaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'reCAPTCHA detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'hcaptcha') {\n            // Look for hCaptcha\n            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');\n            if (hcaptcha) {\n              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');\n              result = {\n                success: false,\n                type: 'hcaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'hCaptcha detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'text') {\n            // Look for text-based CAPTCHA\n            const textCaptcha = await page.$('img[src*=\"captcha\"], img[alt*=\"captcha\"], .captcha-image');\n            if (textCaptcha) {\n              result = {\n                success: false,\n                type: 'text',\n                detected: true,\n                message: 'Text CAPTCHA detected but solving not implemented yet'\n              };\n            }\n          }\n\n          // If no CAPTCHA detected\n          if (!result.detected) {\n            result = {\n              success: true,\n              type: captchaType,\n              detected: false,\n              message: 'No CAPTCHA detected on page'\n            };\n          }\n        } catch (error) {\n          result = {\n            success: false,\n            type: captchaType,\n            error: error.message\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            captchaResult: result,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Execute custom JavaScript with advanced capabilities\n   */ async executeAdvancedScript(url, script, options) {\n        const waitForSelector = options?.waitForSelector || '';\n        const timeout = options?.timeout || 30000;\n        const returnType = options?.returnType || 'json';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        ${waitForSelector ? `await page.waitForSelector(\"${waitForSelector}\", { timeout: ${timeout} });` : ''}\n\n        // Execute custom script\n        const scriptResult = await page.evaluate(() => {\n          ${script}\n        });\n\n        let finalResult = scriptResult;\n\n        if (\"${returnType}\" === 'screenshot') {\n          const screenshot = await page.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n          finalResult = {\n            scriptResult: scriptResult,\n            screenshot: screenshot\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            result: finalResult,\n            returnType: \"${returnType}\",\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Smart content extraction with multiple strategies\n   */ async smartExtract(url, extractionGoals) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const goals = ${JSON.stringify(extractionGoals)};\n        const results = {};\n\n        // Common extraction patterns\n        const extractors = {\n          prices: () => {\n            const priceSelectors = [\n              '[class*=\"price\"]', '[id*=\"price\"]', '.cost', '.amount',\n              '[data-testid*=\"price\"]', '.currency', '[class*=\"dollar\"]'\n            ];\n            const prices = [];\n            priceSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const text = el.textContent?.trim();\n                if (text && /[$£€¥₹]|\\\\d+\\\\.\\\\d{2}/.test(text)) {\n                  prices.push({\n                    text: text,\n                    selector: selector,\n                    element: el.tagName\n                  });\n                }\n              });\n            });\n            return prices;\n          },\n\n          contact: () => {\n            const contactSelectors = [\n              '[href^=\"mailto:\"]', '[href^=\"tel:\"]', '.contact', '.email', '.phone'\n            ];\n            const contacts = [];\n            contactSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                contacts.push({\n                  text: el.textContent?.trim(),\n                  href: el.getAttribute('href'),\n                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'\n                });\n              });\n            });\n            return contacts;\n          },\n\n          products: () => {\n            const productSelectors = [\n              '.product', '[class*=\"product\"]', '.item', '[data-testid*=\"product\"]'\n            ];\n            const products = [];\n            productSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const title = el.querySelector('h1, h2, h3, .title, [class*=\"title\"]')?.textContent?.trim();\n                const price = el.querySelector('[class*=\"price\"], .cost')?.textContent?.trim();\n                const image = el.querySelector('img')?.src;\n                if (title) {\n                  products.push({ title, price, image });\n                }\n              });\n            });\n            return products;\n          },\n\n          text: () => {\n            // Extract main content\n            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];\n            let content = '';\n            for (const selector of contentSelectors) {\n              const el = document.querySelector(selector);\n              if (el) {\n                content = el.textContent?.trim() || '';\n                break;\n              }\n            }\n            if (!content) {\n              content = document.body.textContent?.trim() || '';\n            }\n            return content.substring(0, 5000); // Limit to 5000 chars\n          },\n\n          links: () => {\n            const links = [];\n            document.querySelectorAll('a[href]').forEach(el => {\n              const href = el.getAttribute('href');\n              const text = el.textContent?.trim();\n              if (href && text && !href.startsWith('#')) {\n                links.push({\n                  url: new URL(href, window.location.href).href,\n                  text: text\n                });\n              }\n            });\n            return links.slice(0, 50); // Limit to 50 links\n          }\n        };\n\n        // Execute extractors based on goals\n        goals.forEach(goal => {\n          const goalLower = goal.toLowerCase();\n          if (goalLower.includes('price') || goalLower.includes('cost')) {\n            results.prices = extractors.prices();\n          }\n          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {\n            results.contact = extractors.contact();\n          }\n          if (goalLower.includes('product') || goalLower.includes('item')) {\n            results.products = extractors.products();\n          }\n          if (goalLower.includes('text') || goalLower.includes('content')) {\n            results.text = extractors.text();\n          }\n          if (goalLower.includes('link') || goalLower.includes('url')) {\n            results.links = extractors.links();\n          }\n        });\n\n        // If no specific goals, extract everything\n        if (goals.length === 0) {\n          Object.keys(extractors).forEach(key => {\n            results[key] = extractors[key]();\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            extractionGoals: goals,\n            results: results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    // Get service statistics\n    getStats() {\n        return {\n            totalKeys: this.apiKeys.length,\n            healthyKeys: this.getHealthyKeys().length,\n            keyUsage: Object.fromEntries(this.keyUsageCount),\n            keyErrors: Object.fromEntries(this.keyErrors)\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowserlessService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browserless.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/BrowsingExecutionService.ts":
/*!******************************************************!*\
  !*** ./src/lib/browsing/BrowsingExecutionService.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrowsingExecutionService: () => (/* binding */ BrowsingExecutionService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n/* harmony import */ var _SmartBrowsingExecutor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SmartBrowsingExecutor */ \"(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts\");\n// Browsing Execution Service - Handles browsing model selection with fallback support\n// Integrates with BrowserlessService for actual web browsing\n\n\nclass BrowsingExecutionService {\n    constructor(){\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n        this.smartBrowsingExecutor = _SmartBrowsingExecutor__WEBPACK_IMPORTED_MODULE_1__.SmartBrowsingExecutor.getInstance();\n    }\n    static getInstance() {\n        if (!BrowsingExecutionService.instance) {\n            BrowsingExecutionService.instance = new BrowsingExecutionService();\n        }\n        return BrowsingExecutionService.instance;\n    }\n    /**\n   * Execute browsing with model fallback support\n   * Now uses SmartBrowsingExecutor for intelligent, plan-based browsing\n   */ async executeBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery, useSmartBrowsing = true, progressCallback) {\n        try {\n            if (!browsingConfig.browsing_enabled) {\n                return {\n                    success: false,\n                    error: 'Browsing is not enabled for this configuration'\n                };\n            }\n            if (!browsingConfig.browsing_models || browsingConfig.browsing_models.length === 0) {\n                return {\n                    success: false,\n                    error: 'No browsing models configured'\n                };\n            }\n            console.log(`[Browsing Execution] Starting ${useSmartBrowsing ? 'SMART' : 'SIMPLE'} browsing`);\n            console.log(`[Browsing Execution] Query: \"${query}\", Type: ${browsingType}`);\n            console.log(`[Browsing Execution] Refined query: \"${refinedQuery || 'none'}\"`);\n            console.log(`[Browsing Execution] Available models: ${browsingConfig.browsing_models.length}`);\n            // Use Smart Browsing for complex tasks\n            if (useSmartBrowsing) {\n                console.log(`[Browsing Execution] 🧠 Using Smart Browsing Executor`);\n                const smartResult = await this.smartBrowsingExecutor.executeSmartBrowsing(refinedQuery || query, browsingConfig, browsingType, progressCallback);\n                console.log(`[Browsing Execution] Smart browsing result:`, {\n                    success: smartResult.success,\n                    hasContent: !!smartResult.content,\n                    contentLength: smartResult.content?.length || 0,\n                    error: smartResult.error\n                });\n                if (smartResult.success) {\n                    return {\n                        success: true,\n                        content: smartResult.content,\n                        modelUsed: browsingConfig.browsing_models[0]?.model || 'smart-browsing',\n                        providerUsed: browsingConfig.browsing_models[0]?.provider || 'smart-browsing',\n                        browsingData: smartResult.plan\n                    };\n                } else {\n                    // Check if we should fallback due to network issues\n                    if (smartResult.shouldFallback) {\n                        console.log(`[Browsing Execution] 🌐 Network issue detected, falling back to simple browsing: ${smartResult.error}`);\n                    } else {\n                        console.log(`[Browsing Execution] Smart browsing failed, falling back to simple browsing: ${smartResult.error}`);\n                    }\n                // Fall back to simple browsing\n                }\n            }\n            // Fallback to simple browsing (original logic)\n            console.log(`[Browsing Execution] 🔄 Using Simple Browsing (fallback)`);\n            return await this.executeSimpleBrowsing(query, browsingConfig, browsingType, refinedQuery);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Fatal error:', errorMessage);\n            return {\n                success: false,\n                error: `Browsing execution failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Execute simple browsing (original logic) as fallback\n   */ async executeSimpleBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery) {\n        // Sort models by order for fallback\n        const sortedModels = [\n            ...browsingConfig.browsing_models\n        ].sort((a, b)=>a.order - b.order);\n        console.log(`[Simple Browsing] Starting with ${sortedModels.length} models configured`);\n        let lastError = null;\n        // Try each model in order (strict fallback pattern)\n        for (const model of sortedModels){\n            try {\n                console.log(`[Simple Browsing] Attempting with ${model.provider}/${model.model}`);\n                // First, perform the web browsing\n                const browsingResult = await this.performWebBrowsing(refinedQuery || query, browsingType);\n                if (!browsingResult.success) {\n                    throw new Error(browsingResult.error || 'Browsing failed');\n                }\n                console.log(`[Simple Browsing] ✅ Web browsing successful, got ${JSON.stringify(browsingResult.data).length} characters of data`);\n                // Then, use the AI model to process the browsing results\n                const aiResult = await this.processWithAI(query, browsingResult.data, model, browsingType);\n                if (aiResult.success) {\n                    console.log(`[Simple Browsing] ✅ Success with ${model.provider}/${model.model}`);\n                    return {\n                        success: true,\n                        content: aiResult.content,\n                        modelUsed: model.model,\n                        providerUsed: model.provider,\n                        browsingData: browsingResult.data\n                    };\n                } else {\n                    throw new Error(aiResult.error || 'AI processing failed');\n                }\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n                lastError = errorMessage;\n                console.log(`[Simple Browsing] ❌ Failed with ${model.provider}/${model.model}: ${errorMessage}`);\n                continue;\n            }\n        }\n        // If we get here, all models failed\n        return {\n            success: false,\n            error: `All browsing models failed. Last error: ${lastError}`\n        };\n    }\n    /**\n   * Perform web browsing using BrowserlessService\n   */ async performWebBrowsing(query, browsingType) {\n        try {\n            let result;\n            switch(browsingType){\n                case 'search':\n                    // Use search functionality\n                    result = await this.browserlessService.searchAndExtractUnblocked(query);\n                    break;\n                case 'navigate':\n                    // Try to extract URL from query or use as-is\n                    const urlMatch = query.match(/https?:\\/\\/[^\\s]+/);\n                    const url = urlMatch ? urlMatch[0] : query;\n                    result = await this.browserlessService.navigateAndExtract(url);\n                    break;\n                case 'extract':\n                    // Similar to navigate but with specific extraction\n                    const extractUrl = query.match(/https?:\\/\\/[^\\s]+/)?.[0] || query;\n                    result = await this.browserlessService.navigateAndExtract(extractUrl);\n                    break;\n                default:\n                    // Default to search\n                    result = await this.browserlessService.searchAndExtractUnblocked(query);\n            }\n            if (result && result.data) {\n                console.log(`[Browsing Execution] ✅ Web browsing successful, got ${JSON.stringify(result.data).length} characters of data`);\n                return {\n                    success: true,\n                    data: result.data\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No data returned from browsing'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Web browsing failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Process browsing results with AI model\n   */ async processWithAI(originalQuery, browsingData, model, browsingType) {\n        try {\n            const prompt = this.buildProcessingPrompt(originalQuery, browsingData, browsingType);\n            // Call the appropriate AI provider\n            const response = await this.callAIProvider(prompt, model);\n            if (response && response.content) {\n                return {\n                    success: true,\n                    content: response.content\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No content returned from AI model'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Build processing prompt for AI model\n   */ buildProcessingPrompt(originalQuery, browsingData, browsingType) {\n        const dataStr = JSON.stringify(browsingData, null, 2);\n        // Get current date and time for context\n        const now = new Date();\n        const currentDateTime = now.toLocaleString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit',\n            timeZoneName: 'short'\n        });\n        return `You are an AI assistant that processes REAL-TIME web browsing results to answer user queries. You must ONLY use the browsing data provided below - do NOT use your training data or general knowledge.\n\nCURRENT DATE & TIME: ${currentDateTime}\nUSER QUERY: \"${originalQuery}\"\nBROWSING TYPE: ${browsingType}\n\n=== REAL-TIME WEB BROWSING RESULTS ===\n${dataStr}\n=== END BROWSING RESULTS ===\n\nCRITICAL INSTRUCTIONS:\n1. ONLY use information from the browsing results above - do NOT use your training data\n2. If the browsing results don't contain the information needed, clearly state \"The browsing results do not contain sufficient information to answer this query\"\n3. When providing specific details (times, dates, prices, schedules), ONLY use data from the browsing results\n4. Include specific details, numbers, dates, and facts ONLY from the browsing results\n5. Cite sources when possible (URLs, website names from the browsing data)\n6. If you find conflicting information in the browsing results, mention the discrepancies\n7. Prioritize the most recent and relevant information from the browsing results\n8. For time-sensitive queries, emphasize that the information is current as of ${currentDateTime}\n\nIMPORTANT: Do NOT make assumptions or use general knowledge. Base your response ENTIRELY on the browsing data provided above.\n\nPlease provide a response based ONLY on the browsing results:`;\n    }\n    /**\n   * Get the correct model ID for API calls (following RouKey's pattern)\n   * OpenRouter keeps full model ID, other providers strip the prefix\n   */ getEffectiveModelId(model) {\n        // For OpenRouter, return the full model ID\n        if (model.provider.toLowerCase() === 'openrouter') {\n            return model.model;\n        }\n        // For other providers, extract the model name after the prefix\n        const parts = model.model.split('/');\n        return parts.length > 1 ? parts[parts.length - 1] : model.model;\n    }\n    /**\n   * Call AI provider based on model configuration\n   */ async callAIProvider(prompt, model) {\n        try {\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            // Get the effective model ID following RouKey's pattern\n            const effectiveModelId = this.getEffectiveModelId(model);\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'openrouter':\n                    apiUrl = 'https://openrouter.ai/api/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`,\n                        'HTTP-Referer': 'https://roukey.online',\n                        'X-Title': 'RouKey'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider: ${model.provider}`);\n            }\n            console.log(`[Browsing AI] Calling ${model.provider} API with model ${effectiveModelId} (original: ${model.model})...`);\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000) // 30s timeout\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(`[Browsing AI] API error: ${response.status} - ${errorText}`);\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            console.log(`[Browsing AI] Raw response:`, JSON.stringify(result, null, 2));\n            // Extract content based on provider response format\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                // For OpenAI-compatible APIs (including Google's OpenAI-compatible endpoint)\n                content = result.choices?.[0]?.message?.content;\n            }\n            console.log(`[Browsing AI] Extracted content length: ${content?.length || 0}`);\n            if (!content || content.trim().length === 0) {\n                console.error(`[Browsing AI] No content extracted from response. Full response:`, result);\n                return {\n                    error: 'No content returned from AI model - empty response'\n                };\n            }\n            return {\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error(`[Browsing AI] Error calling AI provider:`, errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowsingExecutionService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/BrowsingExecutionService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts":
/*!***************************************************!*\
  !*** ./src/lib/browsing/SmartBrowsingExecutor.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartBrowsingExecutor: () => (/* binding */ SmartBrowsingExecutor)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n// Smart Browsing Executor - Intelligent plan-based browsing with todo list management\n// Handles complex browsing tasks by creating plans, executing subtasks, and updating progress\n\nclass SmartBrowsingExecutor {\n    constructor(){\n        this.activePlans = new Map();\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n    }\n    static getInstance() {\n        if (!SmartBrowsingExecutor.instance) {\n            SmartBrowsingExecutor.instance = new SmartBrowsingExecutor();\n        }\n        return SmartBrowsingExecutor.instance;\n    }\n    /**\n   * Execute smart browsing with planning and todo list management\n   */ async executeSmartBrowsing(query, browsingConfig, browsingType = 'search', progressCallback) {\n        try {\n            console.log(`[Smart Browsing] 🎯 Starting intelligent browsing for: \"${query}\"`);\n            // Step 1: Create a browsing plan\n            const plan = await this.createBrowsingPlan(query, browsingType, browsingConfig);\n            this.activePlans.set(plan.id, plan);\n            console.log(`[Smart Browsing] 📋 Created plan with ${plan.subtasks.length} subtasks`);\n            this.logPlan(plan);\n            // Notify plan creation\n            progressCallback?.onPlanCreated?.(plan);\n            // Step 2: Execute the plan\n            const result = await this.executePlan(plan, browsingConfig, progressCallback);\n            if (result.success) {\n                console.log(`[Smart Browsing] ✅ Plan completed successfully`);\n                return {\n                    success: true,\n                    content: result.content,\n                    plan: plan\n                };\n            } else {\n                console.log(`[Smart Browsing] ❌ Plan failed: ${result.error}`);\n                // Check if this is a network connectivity issue\n                if (this.isNetworkError(result.error)) {\n                    console.log(`[Smart Browsing] 🌐 Network connectivity issue detected, recommending fallback to simple browsing`);\n                    return {\n                        success: false,\n                        error: `Network connectivity issue: ${result.error}. Falling back to simple browsing.`,\n                        plan: plan,\n                        shouldFallback: true\n                    };\n                }\n                return {\n                    success: false,\n                    error: result.error,\n                    plan: plan\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Smart Browsing] Fatal error:', errorMessage);\n            // Check if this is a network connectivity issue\n            if (this.isNetworkError(errorMessage)) {\n                console.log(`[Smart Browsing] 🌐 Network connectivity issue detected, recommending fallback to simple browsing`);\n                return {\n                    success: false,\n                    error: `Network connectivity issue: ${errorMessage}. Falling back to simple browsing.`,\n                    shouldFallback: true\n                };\n            }\n            return {\n                success: false,\n                error: `Smart browsing failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Check if an error is related to network connectivity\n   */ isNetworkError(errorMessage) {\n        const networkErrorPatterns = [\n            'fetch failed',\n            'ECONNRESET',\n            'ENOTFOUND',\n            'ETIMEDOUT',\n            'ECONNREFUSED',\n            'Network request failed',\n            'Connection timeout',\n            'DNS resolution failed'\n        ];\n        return networkErrorPatterns.some((pattern)=>errorMessage.toLowerCase().includes(pattern.toLowerCase()));\n    }\n    /**\n   * Create an intelligent browsing plan based on the query\n   */ async createBrowsingPlan(query, browsingType, browsingConfig) {\n        const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        // Use AI to create a smart plan\n        const planningResult = await this.generatePlanWithAI(query, browsingType, browsingConfig);\n        const plan = {\n            id: planId,\n            originalQuery: query,\n            goal: planningResult.goal || `Find comprehensive information about: ${query}`,\n            subtasks: planningResult.subtasks || this.createFallbackPlan(query, browsingType),\n            status: 'planning',\n            progress: 0,\n            gatheredData: {},\n            visitedUrls: [],\n            searchQueries: [],\n            completedSubtasks: [],\n            failedSubtasks: [],\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        return plan;\n    }\n    /**\n   * Generate a browsing plan using AI\n   */ async generatePlanWithAI(query, browsingType, browsingConfig) {\n        try {\n            const model = browsingConfig.browsing_models[0]; // Use first available model for planning\n            if (!model) {\n                throw new Error('No browsing models available for planning');\n            }\n            const planningPrompt = this.buildPlanningPrompt(query, browsingType);\n            const aiResult = await this.callAIForPlanning(planningPrompt, model);\n            if (aiResult.success && aiResult.content) {\n                return this.parsePlanFromAI(aiResult.content, query);\n            } else {\n                console.warn('[Smart Browsing] AI planning failed, using fallback plan');\n                return {\n                    goal: `Find information about: ${query}`,\n                    subtasks: this.createFallbackPlan(query, browsingType)\n                };\n            }\n        } catch (error) {\n            console.warn('[Smart Browsing] AI planning error, using fallback:', error);\n            return {\n                goal: `Find information about: ${query}`,\n                subtasks: this.createFallbackPlan(query, browsingType)\n            };\n        }\n    }\n    /**\n   * Build a comprehensive planning prompt for AI\n   */ buildPlanningPrompt(query, browsingType) {\n        // Get current date and time for context\n        const now = new Date();\n        const currentDateTime = now.toLocaleString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit',\n            timeZoneName: 'short'\n        });\n        const currentYear = now.getFullYear();\n        const currentMonth = now.toLocaleString('en-US', {\n            month: 'long'\n        });\n        return `You are an expert web browsing strategist. Create a detailed browsing plan to thoroughly research this query: \"${query}\"\n\nCURRENT DATE & TIME: ${currentDateTime}\nCURRENT YEAR: ${currentYear}\nCURRENT MONTH: ${currentMonth}\n\nBROWSING TYPE: ${browsingType}\n\nIMPORTANT: When generating search terms and subtasks, consider the current date and time context:\n- For recent events, include \"${currentYear}\" in search terms\n- For current trends, use \"latest\", \"recent\", \"${currentMonth} ${currentYear}\"\n- For news queries, prioritize recent timeframes\n- For technology/AI topics, include current year for latest developments\n- For market/business queries, focus on current and recent data\n\nCreate a JSON response with this structure:\n{\n  \"goal\": \"Clear statement of what we want to achieve\",\n  \"subtasks\": [\n    {\n      \"id\": \"unique_id\",\n      \"type\": \"search|navigate|extract|analyze\",\n      \"description\": \"What this subtask does\",\n      \"query\": \"Specific search query or URL\",\n      \"priority\": 1-10,\n      \"searchTerms\": [\"alternative\", \"search\", \"terms\"],\n      \"expectedInfo\": \"What information we expect to find\"\n    }\n  ]\n}\n\nGUIDELINES:\n1. Start with broad searches, then get more specific\n2. Use multiple search strategies and terms with temporal context\n3. Include fact-checking and verification steps\n4. Plan for 3-7 subtasks maximum\n5. Make search terms diverse, comprehensive, and time-aware\n6. Consider different angles and perspectives\n7. Include analysis steps to synthesize information\n8. For \"navigate\" tasks, only use if you have specific URLs (https://...)\n9. Most tasks should be \"search\" type for better reliability\n10. ALWAYS include temporal keywords when relevant:\n    - For news: \"latest news\", \"recent updates\", \"${currentMonth} ${currentYear}\"\n    - For trends: \"current trends\", \"${currentYear} trends\"\n    - For technology: \"latest developments\", \"${currentYear} updates\"\n    - For data/statistics: \"current data\", \"recent statistics\", \"${currentYear} data\"\n\nCreate a smart, thorough, and temporally-aware plan:`;\n    }\n    /**\n   * Enhance query with temporal context for better search results\n   */ enhanceQueryWithTemporal(query, temporalKeywords) {\n        const currentYear = new Date().getFullYear();\n        const currentMonth = new Date().toLocaleString('en-US', {\n            month: 'long'\n        });\n        // Detect if query already has temporal context\n        const hasTemporalContext = /\\b(latest|recent|current|new|today|\\d{4}|now)\\b/i.test(query);\n        return {\n            primary: query,\n            temporal: hasTemporalContext ? query : `${query} ${currentYear} latest`,\n            alternatives: [\n                query,\n                `${query} information`,\n                `${query} details`,\n                hasTemporalContext ? `${query} overview` : `${query} ${currentYear}`\n            ],\n            recentTerms: [\n                `${query} recent`,\n                `${query} latest news`,\n                `${query} ${currentMonth} ${currentYear}`,\n                `${query} current trends`\n            ]\n        };\n    }\n    /**\n   * Create a fallback plan when AI planning fails\n   */ createFallbackPlan(query, browsingType) {\n        const baseId = Date.now();\n        const currentYear = new Date().getFullYear();\n        const currentMonth = new Date().toLocaleString('en-US', {\n            month: 'long'\n        });\n        // Add temporal context to search terms\n        const temporalKeywords = [\n            `${currentYear}`,\n            `latest`,\n            `recent`,\n            `${currentMonth} ${currentYear}`\n        ];\n        const enhancedQuery = this.enhanceQueryWithTemporal(query, temporalKeywords);\n        return [\n            {\n                id: `search_${baseId}_1`,\n                type: 'search',\n                description: 'Primary search for main topic',\n                query: enhancedQuery.primary,\n                status: 'pending',\n                priority: 10,\n                attempts: 0,\n                maxAttempts: 3,\n                searchTerms: enhancedQuery.alternatives,\n                expectedInfo: 'General information about the topic'\n            },\n            {\n                id: `search_${baseId}_2`,\n                type: 'search',\n                description: 'Secondary search with temporal context',\n                query: enhancedQuery.temporal,\n                status: 'pending',\n                priority: 8,\n                attempts: 0,\n                maxAttempts: 3,\n                searchTerms: enhancedQuery.recentTerms,\n                expectedInfo: 'Recent developments and current information'\n            },\n            {\n                id: `analyze_${baseId}`,\n                type: 'analyze',\n                description: 'Analyze and synthesize gathered information',\n                query: 'synthesize findings',\n                status: 'pending',\n                priority: 5,\n                attempts: 0,\n                maxAttempts: 1,\n                dependencies: [\n                    `search_${baseId}_1`,\n                    `search_${baseId}_2`\n                ],\n                expectedInfo: 'Comprehensive summary of findings'\n            }\n        ];\n    }\n    /**\n   * Execute the browsing plan step by step\n   */ async executePlan(plan, browsingConfig, progressCallback) {\n        plan.status = 'executing';\n        plan.updatedAt = new Date().toISOString();\n        console.log(`[Smart Browsing] 🚀 Starting plan execution`);\n        try {\n            // Execute subtasks in priority order, respecting dependencies\n            while(this.hasRemainingTasks(plan)){\n                const nextTask = this.getNextExecutableTask(plan);\n                if (!nextTask) {\n                    console.log(`[Smart Browsing] ⏸️ No executable tasks remaining, checking if plan is complete`);\n                    break;\n                }\n                console.log(`[Smart Browsing] 🔄 Executing subtask: ${nextTask.description}`);\n                plan.currentSubtask = nextTask.id;\n                nextTask.status = 'in_progress';\n                // Enhance the subtask with context-aware query building\n                const enhancedTask = this.enhanceSubtaskQuery(nextTask, plan);\n                // Update the task in the plan with enhanced version\n                const taskIndex = plan.subtasks.findIndex((task)=>task.id === nextTask.id);\n                if (taskIndex !== -1) {\n                    plan.subtasks[taskIndex] = enhancedTask;\n                }\n                // Notify task started\n                progressCallback?.onTaskStarted?.(enhancedTask, plan);\n                progressCallback?.onStatusUpdate?.(`Executing: ${enhancedTask.description}`, plan);\n                const taskResult = await this.executeSubtask(enhancedTask, plan, browsingConfig);\n                if (taskResult.success) {\n                    nextTask.status = 'completed';\n                    nextTask.result = taskResult.data;\n                    plan.completedSubtasks.push(nextTask.id);\n                    // Store result in plan's gathered data for context passing\n                    plan.gatheredData[nextTask.id] = {\n                        taskType: nextTask.type,\n                        description: nextTask.description,\n                        query: nextTask.query,\n                        result: taskResult.data,\n                        completedAt: new Date().toISOString()\n                    };\n                    console.log(`[Smart Browsing] ✅ Subtask completed: ${nextTask.description}`);\n                    console.log(`[Smart Browsing] 📊 Stored context data for future subtasks`);\n                    // Notify task completed\n                    progressCallback?.onTaskCompleted?.(nextTask, plan);\n                } else {\n                    nextTask.attempts++;\n                    nextTask.error = taskResult.error;\n                    if (nextTask.attempts >= nextTask.maxAttempts) {\n                        nextTask.status = 'failed';\n                        plan.failedSubtasks.push(nextTask.id);\n                        console.log(`[Smart Browsing] ❌ Subtask failed permanently: ${nextTask.description}`);\n                        // Notify task failed\n                        progressCallback?.onTaskFailed?.(nextTask, plan);\n                    } else {\n                        nextTask.status = 'pending';\n                        console.log(`[Smart Browsing] 🔄 Subtask failed, will retry (${nextTask.attempts}/${nextTask.maxAttempts}): ${nextTask.description}`);\n                    }\n                }\n                // Update progress\n                plan.progress = this.calculateProgress(plan);\n                progressCallback?.onProgressUpdate?.(plan.progress, plan);\n                plan.updatedAt = new Date().toISOString();\n                this.logProgress(plan);\n            }\n            // Generate final result\n            const finalResult = await this.synthesizeFinalResult(plan, browsingConfig);\n            plan.finalResult = finalResult;\n            plan.status = 'completed';\n            plan.progress = 100;\n            // Notify plan completed\n            progressCallback?.onPlanCompleted?.(plan);\n            progressCallback?.onStatusUpdate?.('Browsing completed successfully!', plan);\n            return {\n                success: true,\n                content: finalResult\n            };\n        } catch (error) {\n            plan.status = 'failed';\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Smart Browsing] Plan execution failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Check if there are remaining tasks to execute\n   */ hasRemainingTasks(plan) {\n        return plan.subtasks.some((task)=>task.status === 'pending' || task.status === 'in_progress');\n    }\n    /**\n   * Get the next executable task (highest priority, dependencies met)\n   */ getNextExecutableTask(plan) {\n        const executableTasks = plan.subtasks.filter((task)=>{\n            if (task.status !== 'pending') return false;\n            // Check if dependencies are met\n            if (task.dependencies && task.dependencies.length > 0) {\n                return task.dependencies.every((depId)=>plan.completedSubtasks.includes(depId));\n            }\n            return true;\n        });\n        // Sort by priority (highest first)\n        executableTasks.sort((a, b)=>b.priority - a.priority);\n        return executableTasks[0] || null;\n    }\n    /**\n   * Execute a single subtask\n   */ async executeSubtask(subtask, plan, browsingConfig) {\n        try {\n            switch(subtask.type){\n                case 'search':\n                    return await this.executeSearchSubtask(subtask, plan);\n                case 'navigate':\n                    return await this.executeNavigateSubtask(subtask, plan);\n                case 'extract':\n                    return await this.executeExtractSubtask(subtask, plan);\n                case 'analyze':\n                    return await this.executeAnalyzeSubtask(subtask, plan, browsingConfig);\n                default:\n                    throw new Error(`Unknown subtask type: ${subtask.type}`);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Execute a search subtask with advanced BrowserQL capabilities and smart query refinement\n   */ async executeSearchSubtask(subtask, plan) {\n        // Build context-aware query using results from previous subtasks\n        const contextualQuery = this.buildContextualQuery(subtask, plan);\n        const searchTerms = [\n            contextualQuery,\n            subtask.query,\n            ...subtask.searchTerms || []\n        ];\n        let lastError = '';\n        let bestResults = null;\n        let bestScore = 0;\n        console.log(`[Smart Browsing] 🔍 Advanced search execution for: \"${contextualQuery}\"`);\n        console.log(`[Smart Browsing] 🧠 Using context from ${plan.completedSubtasks.length} completed subtasks`);\n        // Check if this requires complex automation (flight booking, form filling, etc.)\n        const requiresComplexAutomation = this.detectComplexAutomationNeeds(subtask, contextualQuery);\n        if (requiresComplexAutomation) {\n            console.log(`[Smart Browsing] 🤖 Detected complex automation needs for: ${contextualQuery}`);\n            return await this.executeComplexAutomationSearch(subtask, plan, contextualQuery);\n        }\n        // Try different search terms with enhanced result evaluation\n        for (const searchTerm of searchTerms){\n            try {\n                console.log(`[Smart Browsing] 🔍 Searching with enhanced parsing: \"${searchTerm}\"`);\n                plan.searchQueries.push(searchTerm);\n                // Use the enhanced search with better snippet extraction\n                const result = await this.browserlessService.searchAndExtractUnblocked(searchTerm);\n                if (result.data && result.data.results && result.data.results.length > 0) {\n                    console.log(`[Smart Browsing] ✅ Found ${result.data.results.length} results with enhanced snippets for: \"${searchTerm}\"`);\n                    // Calculate result quality score based on snippet content and relevance\n                    const resultScore = this.calculateResultQuality(result.data.results, subtask.query);\n                    console.log(`[Smart Browsing] 📊 Result quality score: ${resultScore.toFixed(2)} for \"${searchTerm}\"`);\n                    // Store URLs we've found\n                    result.data.results.forEach((item)=>{\n                        if (item.link && !plan.visitedUrls.includes(item.link)) {\n                            plan.visitedUrls.push(item.link);\n                        }\n                    });\n                    // Keep track of best results\n                    if (resultScore > bestScore) {\n                        bestScore = resultScore;\n                        bestResults = result.data;\n                    }\n                    // If we found high-quality results, use them immediately\n                    if (resultScore > 0.8) {\n                        console.log(`[Smart Browsing] 🎯 High-quality results found (score: ${resultScore.toFixed(2)}), using immediately`);\n                        return {\n                            success: true,\n                            data: result.data\n                        };\n                    }\n                } else {\n                    lastError = `No results found for: \"${searchTerm}\"`;\n                    console.log(`[Smart Browsing] ⚠️ ${lastError}`);\n                }\n            } catch (error) {\n                lastError = error instanceof Error ? error.message : 'Search failed';\n                console.log(`[Smart Browsing] ❌ Search error for \"${searchTerm}\": ${lastError}`);\n            }\n        }\n        // Return best results if we found any\n        if (bestResults && bestScore > 0.3) {\n            console.log(`[Smart Browsing] ✅ Using best results with score: ${bestScore.toFixed(2)}`);\n            return {\n                success: true,\n                data: bestResults\n            };\n        }\n        return {\n            success: false,\n            error: `All search terms failed to find quality results. Last error: ${lastError}`\n        };\n    }\n    /**\n   * Calculate result quality score based on snippet content and relevance\n   */ calculateResultQuality(results, originalQuery) {\n        if (!results || results.length === 0) return 0;\n        let totalScore = 0;\n        const queryWords = originalQuery.toLowerCase().split(/\\s+/).filter((word)=>word.length > 2);\n        const currentYear = new Date().getFullYear().toString();\n        for (const result of results){\n            let score = 0;\n            const title = (result.title || '').toLowerCase();\n            const snippet = (result.snippet || '').toLowerCase();\n            const combinedText = `${title} ${snippet}`;\n            // Base score for having content\n            if (snippet && snippet !== 'no description available') {\n                score += 0.3;\n            }\n            // Relevance score based on query word matches\n            const matchedWords = queryWords.filter((word)=>combinedText.includes(word));\n            score += matchedWords.length / queryWords.length * 0.4;\n            // Bonus for current year (indicates recent/current information)\n            if (combinedText.includes(currentYear)) {\n                score += 0.2;\n            }\n            // Bonus for temporal keywords indicating current information\n            const temporalKeywords = [\n                'latest',\n                'recent',\n                'current',\n                'new',\n                '2025',\n                'updated'\n            ];\n            const temporalMatches = temporalKeywords.filter((keyword)=>combinedText.includes(keyword));\n            score += Math.min(temporalMatches.length * 0.1, 0.3);\n            // Penalty for very short snippets\n            if (snippet.length < 50) {\n                score *= 0.7;\n            }\n            // Bonus for longer, more informative snippets\n            if (snippet.length > 150) {\n                score += 0.1;\n            }\n            totalScore += score;\n        }\n        return Math.min(totalScore / results.length, 1.0);\n    }\n    /**\n   * Execute navigate subtask with advanced automation capabilities\n   */ async executeNavigateSubtask(subtask, plan) {\n        try {\n            const query = subtask.query;\n            console.log(`[Smart Browsing] 🌐 Processing advanced navigation task: ${query}`);\n            // Check if the query is a valid URL\n            const urlPattern = /^https?:\\/\\//i;\n            if (urlPattern.test(query)) {\n                // Direct URL navigation with automation detection\n                console.log(`[Smart Browsing] 🌐 Navigating to URL with automation support: ${query}`);\n                if (!plan.visitedUrls.includes(query)) {\n                    plan.visitedUrls.push(query);\n                }\n                // Check if this requires complex automation\n                const requiresComplexAutomation = this.detectComplexAutomationNeeds(subtask, query);\n                if (requiresComplexAutomation) {\n                    console.log(`[Smart Browsing] 🤖 Using complex automation workflow for: ${query}`);\n                    return await this.executeComplexAutomation(subtask, query, plan);\n                } else {\n                    // Standard navigation with enhanced error handling\n                    const result = await this.browserlessService.navigateAndExtract(query);\n                    if (result.data) {\n                        console.log(`[Smart Browsing] ✅ Navigation successful for: ${query}`);\n                        return {\n                            success: true,\n                            data: result.data\n                        };\n                    } else {\n                        return {\n                            success: false,\n                            error: 'No data extracted from navigation'\n                        };\n                    }\n                }\n            } else {\n                // Not a direct URL - convert to search task with enhanced query processing\n                console.log(`[Smart Browsing] 🔄 Converting navigation to enhanced search: ${query}`);\n                // Extract meaningful search terms from the navigation description\n                let searchQuery = query;\n                if (query.toLowerCase().includes('navigate to')) {\n                    searchQuery = query.replace(/navigate to\\s*/i, '').trim();\n                }\n                if (query.toLowerCase().includes('websites of')) {\n                    searchQuery = searchQuery.replace(/websites of\\s*/i, '').trim();\n                }\n                // Add current year for better results\n                const currentYear = new Date().getFullYear();\n                if (!searchQuery.includes(currentYear.toString())) {\n                    searchQuery = `${searchQuery} ${currentYear}`;\n                }\n                // Use the enhanced search functionality\n                return await this.executeSearchSubtask({\n                    ...subtask,\n                    type: 'search',\n                    query: searchQuery,\n                    searchTerms: [\n                        searchQuery,\n                        `${searchQuery} latest`,\n                        `${searchQuery} official website`,\n                        `${searchQuery} information`\n                    ]\n                }, plan);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Navigation failed';\n            console.log(`[Smart Browsing] ❌ Navigation error: ${errorMessage}`);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Detect if a subtask requires complex automation (forms, CAPTCHAs, etc.)\n   */ detectComplexAutomationNeeds(subtask, url) {\n        const description = (subtask.description || '').toLowerCase();\n        const query = (subtask.query || '').toLowerCase();\n        // Keywords that indicate complex automation needs\n        const complexKeywords = [\n            'form',\n            'submit',\n            'login',\n            'register',\n            'book',\n            'reserve',\n            'purchase',\n            'checkout',\n            'payment',\n            'captcha',\n            'verify',\n            'authenticate',\n            'sign in',\n            'sign up',\n            'create account',\n            'fill out',\n            'application',\n            'survey',\n            'reservation',\n            'booking',\n            'order'\n        ];\n        // Flight booking specific keywords\n        const flightBookingKeywords = [\n            'earliest flight',\n            'flight from',\n            'flight to',\n            'book flight',\n            'search flight',\n            'flight booking',\n            'airline',\n            'departure',\n            'arrival',\n            'connecting flight',\n            'flight schedule',\n            'flight search',\n            'travel booking'\n        ];\n        // URL patterns that often require complex automation\n        const complexUrlPatterns = [\n            'login',\n            'register',\n            'checkout',\n            'booking',\n            'reservation',\n            'form',\n            'application',\n            'survey',\n            'account',\n            'dashboard',\n            'admin',\n            'portal'\n        ];\n        // Check for flight booking scenarios\n        const isFlightBooking = flightBookingKeywords.some((keyword)=>description.includes(keyword) || query.includes(keyword));\n        // Check for general complex automation needs\n        const isComplexAutomation = complexKeywords.some((keyword)=>description.includes(keyword) || query.includes(keyword)) || complexUrlPatterns.some((pattern)=>url.toLowerCase().includes(pattern));\n        // Flight booking always requires complex automation for form filling\n        return isFlightBooking || isComplexAutomation;\n    }\n    /**\n   * Execute complex automation workflow using advanced BrowserQL capabilities\n   * Falls back to simple search if automation fails\n   */ async executeComplexAutomation(subtask, url, plan) {\n        try {\n            console.log(`[Smart Browsing] 🤖 Starting complex automation for: ${url}`);\n            // Create a workflow based on the subtask requirements\n            const workflow = this.buildAutomationWorkflow(subtask, url);\n            try {\n                const result = await this.browserlessService.executeComplexWorkflow(url, workflow);\n                if (result && result.data) {\n                    console.log(`[Smart Browsing] ✅ Complex automation successful for: ${url}`);\n                    // Store session info for potential follow-up tasks\n                    if (result.sessionId && result.reconnectUrl) {\n                        plan.sessionInfo = {\n                            sessionId: result.sessionId,\n                            reconnectUrl: result.reconnectUrl,\n                            lastUsed: new Date()\n                        };\n                    }\n                    return {\n                        success: true,\n                        data: result.data\n                    };\n                } else {\n                    throw new Error('Complex automation completed but no data extracted');\n                }\n            } catch (automationError) {\n                const automationErrorMessage = automationError instanceof Error ? automationError.message : 'Complex automation failed';\n                console.log(`[Smart Browsing] ❌ Complex automation failed: ${automationErrorMessage}`);\n                console.log(`[Smart Browsing] 🔄 Falling back to simple search for: ${subtask.description}`);\n                // Fallback to simple search and extraction\n                try {\n                    const searchQuery = this.extractSearchQueryFromSubtask(subtask);\n                    const fallbackResult = await this.browserlessService.searchAndExtractUnblocked(searchQuery);\n                    if (fallbackResult.success && fallbackResult.data) {\n                        console.log(`[Smart Browsing] ✅ Fallback search successful for: ${searchQuery}`);\n                        return {\n                            success: true,\n                            data: fallbackResult.data\n                        };\n                    } else {\n                        throw new Error('Fallback search also failed');\n                    }\n                } catch (fallbackError) {\n                    const fallbackErrorMessage = fallbackError instanceof Error ? fallbackError.message : 'Fallback search failed';\n                    console.log(`[Smart Browsing] ❌ Fallback search failed: ${fallbackErrorMessage}`);\n                    return {\n                        success: false,\n                        error: `Complex automation failed: ${automationErrorMessage}. Fallback search failed: ${fallbackErrorMessage}`\n                    };\n                }\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Complex automation setup failed';\n            console.log(`[Smart Browsing] ❌ Complex automation setup error: ${errorMessage}`);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Build automation workflow steps based on subtask requirements\n   */ buildAutomationWorkflow(subtask, url) {\n        const workflow = [];\n        const description = (subtask.description || '').toLowerCase();\n        const query = (subtask.query || '').toLowerCase();\n        // Always start with navigation\n        workflow.push({\n            name: 'navigate',\n            type: 'navigate',\n            params: {\n                url,\n                waitUntil: 'networkIdle'\n            }\n        });\n        // Add wait for page to stabilize\n        workflow.push({\n            name: 'wait_for_page',\n            type: 'wait',\n            params: {\n                time: 3000\n            }\n        });\n        // Handle cookie consent/popups that might block interaction\n        workflow.push({\n            name: 'handle_cookie_consent',\n            type: 'click',\n            params: {\n                selector: 'button[id*=\"accept\"], button[class*=\"accept\"], button[class*=\"consent\"], .cookie-accept, #cookie-accept, [data-testid*=\"accept\"]',\n                timeout: 5000,\n                optional: true\n            }\n        });\n        // Wait after handling popups\n        workflow.push({\n            name: 'wait_after_popups',\n            type: 'wait',\n            params: {\n                time: 2000\n            }\n        });\n        // Handle CAPTCHA if likely to be present\n        if (description.includes('captcha') || description.includes('verify') || description.includes('challenge') || description.includes('protection')) {\n            workflow.push({\n                name: 'solve_captcha',\n                type: 'captcha',\n                params: {\n                    type: 'hcaptcha'\n                } // Default to hCaptcha, can detect others\n            });\n        }\n        // Handle search functionality\n        if (description.includes('search') || query.includes('search') || description.includes('find') || description.includes('look for')) {\n            workflow.push({\n                name: 'find_search_input',\n                type: 'waitForSelector',\n                params: {\n                    selector: 'input[type=\"search\"], input[name*=\"search\"], input[id*=\"search\"], input[placeholder*=\"search\"], .search-input',\n                    timeout: 10000\n                }\n            });\n            // Extract search terms from the query\n            const searchTerms = this.extractSearchTermsFromQuery(query);\n            if (searchTerms) {\n                workflow.push({\n                    name: 'enter_search_terms',\n                    type: 'type',\n                    params: {\n                        selector: 'input[type=\"search\"], input[name*=\"search\"], input[id*=\"search\"], input[placeholder*=\"search\"], .search-input',\n                        text: searchTerms\n                    }\n                });\n                workflow.push({\n                    name: 'submit_search',\n                    type: 'click',\n                    params: {\n                        selector: 'button[type=\"submit\"], button[class*=\"search\"], .search-button, input[type=\"submit\"]'\n                    }\n                });\n                workflow.push({\n                    name: 'wait_for_search_results',\n                    type: 'wait',\n                    params: {\n                        time: 3000\n                    }\n                });\n            }\n        }\n        // Handle form filling\n        if (description.includes('form') || description.includes('fill') || description.includes('submit') || description.includes('input')) {\n            workflow.push({\n                name: 'wait_for_form',\n                type: 'waitForSelector',\n                params: {\n                    selector: 'form, input, textarea, select',\n                    timeout: 10000\n                }\n            });\n            // Extract form structure for analysis\n            workflow.push({\n                name: 'analyze_form',\n                type: 'extract',\n                params: {\n                    selector: 'form, input, textarea, select',\n                    type: 'html'\n                }\n            });\n        }\n        // Handle login/authentication\n        if (description.includes('login') || description.includes('sign in') || description.includes('authenticate') || description.includes('credentials')) {\n            workflow.push({\n                name: 'wait_for_login_form',\n                type: 'waitForSelector',\n                params: {\n                    selector: 'form[action*=\"login\"], form[id*=\"login\"], input[type=\"password\"], .login-form',\n                    timeout: 10000\n                }\n            });\n            workflow.push({\n                name: 'extract_login_form',\n                type: 'extract',\n                params: {\n                    selector: 'form[action*=\"login\"], form[id*=\"login\"], input[type=\"password\"], .login-form',\n                    type: 'html'\n                }\n            });\n        }\n        // Handle shopping/product pages\n        if (description.includes('product') || description.includes('buy') || description.includes('purchase') || description.includes('shop')) {\n            workflow.push({\n                name: 'extract_product_info',\n                type: 'extract',\n                params: {\n                    selector: '.product, .item, [data-testid*=\"product\"], .price, .title, .description',\n                    type: 'html'\n                }\n            });\n        }\n        // Handle infinite scroll and pagination\n        if (description.includes('scroll') || description.includes('load more') || description.includes('pagination') || description.includes('infinite') || description.includes('more results')) {\n            // First scroll\n            workflow.push({\n                name: 'scroll_content',\n                type: 'scroll',\n                params: {\n                    selector: 'body',\n                    distance: 1000\n                }\n            });\n            workflow.push({\n                name: 'wait_after_scroll',\n                type: 'wait',\n                params: {\n                    time: 2000\n                }\n            });\n            // Second scroll for more content\n            workflow.push({\n                name: 'scroll_more',\n                type: 'scroll',\n                params: {\n                    selector: 'body',\n                    distance: 1000\n                }\n            });\n            workflow.push({\n                name: 'wait_after_second_scroll',\n                type: 'wait',\n                params: {\n                    time: 2000\n                }\n            });\n            // Try to click \"Load More\" button if present\n            workflow.push({\n                name: 'click_load_more',\n                type: 'click',\n                params: {\n                    selector: 'button[class*=\"load\"], button[class*=\"more\"], .load-more, .show-more, [data-testid*=\"load\"]',\n                    timeout: 5000,\n                    optional: true\n                }\n            });\n        }\n        // Handle booking/reservation specific workflows\n        if (description.includes('book') || description.includes('reserve') || description.includes('appointment') || description.includes('schedule')) {\n            workflow.push({\n                name: 'wait_for_booking_interface',\n                type: 'waitForSelector',\n                params: {\n                    selector: '.booking, .reservation, .calendar, .schedule, [class*=\"book\"], [class*=\"reserve\"]',\n                    timeout: 10000\n                }\n            });\n            workflow.push({\n                name: 'extract_booking_options',\n                type: 'extract',\n                params: {\n                    selector: '.booking, .reservation, .calendar, .schedule, [class*=\"book\"], [class*=\"reserve\"]',\n                    type: 'html'\n                }\n            });\n        }\n        // Always end with comprehensive content extraction\n        workflow.push({\n            name: 'extract_main_content',\n            type: 'extract',\n            params: {\n                selector: 'main, .main, .content, .container, body',\n                type: 'text'\n            }\n        });\n        // Extract structured data if available\n        workflow.push({\n            name: 'extract_structured_data',\n            type: 'extract',\n            params: {\n                selector: '[itemscope], [data-*], .product, .article, .post, .result, .listing',\n                type: 'html'\n            }\n        });\n        // Extract navigation and links\n        workflow.push({\n            name: 'extract_navigation',\n            type: 'extract',\n            params: {\n                selector: 'nav, .nav, .navigation, .menu, a[href]',\n                type: 'html'\n            }\n        });\n        // Take screenshot for debugging/verification\n        workflow.push({\n            name: 'take_screenshot',\n            type: 'screenshot',\n            params: {\n                fullPage: false\n            }\n        });\n        console.log(`[Smart Browsing] 🔧 Built automation workflow with ${workflow.length} steps for: ${url}`);\n        return workflow;\n    }\n    /**\n   * Extract search terms from a query string\n   */ extractSearchTermsFromQuery(query) {\n        // Remove common browsing instruction words and extract the actual search terms\n        const cleanQuery = query.replace(/^(search for|find|look for|search|browse|navigate to)\\s+/i, '').replace(/\\s+(on|in|at|from)\\s+\\w+\\.(com|org|net|io).*$/i, '').trim();\n        return cleanQuery.length > 0 ? cleanQuery : null;\n    }\n    /**\n   * Extract search query from subtask for fallback search\n   */ extractSearchQueryFromSubtask(subtask) {\n        // Use the subtask description as the primary search query\n        let searchQuery = subtask.description;\n        // If there's a specific query, use that instead\n        if (subtask.query) {\n            searchQuery = subtask.query;\n        }\n        // Clean up the search query\n        searchQuery = searchQuery.replace(/^(find|search for|look for|determine|analyze)\\s+/i, '').replace(/\\s+(today|now|currently|latest|recent)\\s*/i, ' ').trim();\n        // Add current date context for time-sensitive queries\n        const now = new Date();\n        const currentDate = now.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n        if (searchQuery.includes('today') || searchQuery.includes('earliest') || searchQuery.includes('latest')) {\n            searchQuery += ` ${currentDate}`;\n        }\n        return searchQuery;\n    }\n    /**\n   * Build specialized workflow for flight booking automation\n   */ buildFlightBookingWorkflow(subtask, plan, site) {\n        const workflow = [];\n        const description = subtask.description.toLowerCase();\n        // Get context from previous subtasks\n        const context = this.getFlightBookingContext(plan);\n        console.log(`[Smart Browsing] 🛫 Building flight booking workflow for: ${site}`);\n        console.log(`[Smart Browsing] 📋 Context:`, context);\n        // Navigate to the booking site\n        workflow.push({\n            name: 'navigate_to_booking_site',\n            type: 'navigate',\n            params: {\n                url: site,\n                waitUntil: 'networkIdle'\n            }\n        });\n        // Wait for page to load\n        workflow.push({\n            name: 'wait_for_page_load',\n            type: 'wait',\n            params: {\n                time: 3000\n            }\n        });\n        // Handle cookie banners and popups\n        workflow.push({\n            name: 'dismiss_popups',\n            type: 'click',\n            params: {\n                selector: '[data-testid=\"cookie-banner-close\"], .cookie-banner button, .modal-close, [aria-label=\"Close\"]',\n                optional: true\n            }\n        });\n        // Fill origin airport with site-specific selectors\n        if (context.origin) {\n            const originSelectors = this.getOriginSelectors(site);\n            workflow.push({\n                name: 'fill_origin',\n                type: 'type',\n                params: {\n                    selector: originSelectors.join(', '),\n                    text: context.origin,\n                    clear: true\n                }\n            });\n            // Wait for autocomplete dropdown\n            workflow.push({\n                name: 'wait_for_origin_autocomplete',\n                type: 'wait',\n                params: {\n                    time: 1500\n                }\n            });\n            // Select first autocomplete option\n            workflow.push({\n                name: 'select_origin',\n                type: 'click',\n                params: {\n                    selector: '.autocomplete-item:first-child, .suggestion:first-child, li:first-child, [role=\"option\"]:first-child',\n                    optional: true\n                }\n            });\n        }\n        // Fill destination airport with site-specific selectors\n        if (context.destination) {\n            const destinationSelectors = this.getDestinationSelectors(site);\n            workflow.push({\n                name: 'fill_destination',\n                type: 'type',\n                params: {\n                    selector: destinationSelectors.join(', '),\n                    text: context.destination,\n                    clear: true\n                }\n            });\n            // Wait for autocomplete dropdown\n            workflow.push({\n                name: 'wait_for_destination_autocomplete',\n                type: 'wait',\n                params: {\n                    time: 1500\n                }\n            });\n            // Select first autocomplete option\n            workflow.push({\n                name: 'select_destination',\n                type: 'click',\n                params: {\n                    selector: '.autocomplete-item:first-child, .suggestion:first-child, li:first-child, [role=\"option\"]:first-child',\n                    optional: true\n                }\n            });\n        }\n        // Set departure date (today) with site-specific selectors\n        const dateSelectors = this.getDateSelectors(site);\n        workflow.push({\n            name: 'click_departure_date',\n            type: 'click',\n            params: {\n                selector: dateSelectors.join(', ')\n            }\n        });\n        // Wait for date picker to open\n        workflow.push({\n            name: 'wait_for_date_picker',\n            type: 'wait',\n            params: {\n                time: 1000\n            }\n        });\n        // Select today's date with multiple selector strategies\n        const today = new Date();\n        const todayFormatted = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;\n        const todaySelectors = [\n            `[data-date=\"${todayFormatted}\"]`,\n            `[aria-label*=\"${today.getDate()}\"]`,\n            '.today',\n            '.current-day',\n            '.selected',\n            `button:contains(\"${today.getDate()}\")`,\n            `td:contains(\"${today.getDate()}\")`\n        ];\n        workflow.push({\n            name: 'select_today',\n            type: 'click',\n            params: {\n                selector: todaySelectors.join(', '),\n                optional: true\n            }\n        });\n        // Set one-way trip if this is for connecting flights\n        if (description.includes('connecting') || description.includes('after arrival')) {\n            workflow.push({\n                name: 'select_one_way',\n                type: 'click',\n                params: {\n                    selector: 'input[value=\"oneway\"], label[for*=\"oneway\"], .trip-type .one-way, [data-testid*=\"oneway\"]',\n                    optional: true\n                }\n            });\n        }\n        // Wait before submitting to ensure all fields are filled\n        workflow.push({\n            name: 'wait_before_submit',\n            type: 'wait',\n            params: {\n                time: 2000\n            }\n        });\n        // Submit search with site-specific selectors\n        const searchButtonSelectors = this.getSearchButtonSelectors(site);\n        workflow.push({\n            name: 'submit_search',\n            type: 'click',\n            params: {\n                selector: searchButtonSelectors.join(', ')\n            }\n        });\n        // Wait for results to load\n        workflow.push({\n            name: 'wait_for_results',\n            type: 'wait',\n            params: {\n                time: 5000\n            }\n        });\n        // Handle loading states\n        workflow.push({\n            name: 'wait_for_loading_complete',\n            type: 'waitForSelector',\n            params: {\n                selector: '.flight-results, .search-results, [data-testid=\"results\"]',\n                timeout: 15000\n            }\n        });\n        // Extract flight information\n        workflow.push({\n            name: 'extract_flight_results',\n            type: 'extract',\n            params: {\n                selector: '.flight-result, .flight-option, .search-result, [data-testid*=\"flight\"]',\n                type: 'html'\n            }\n        });\n        // Extract specific flight times and details\n        workflow.push({\n            name: 'extract_flight_details',\n            type: 'extract',\n            params: {\n                selector: '.departure-time, .arrival-time, .flight-duration, .airline-name, .price',\n                type: 'text'\n            }\n        });\n        // Take screenshot for verification\n        workflow.push({\n            name: 'take_results_screenshot',\n            type: 'screenshot',\n            params: {\n                fullPage: false\n            }\n        });\n        console.log(`[Smart Browsing] 🛫 Built flight booking workflow with ${workflow.length} steps`);\n        return workflow;\n    }\n    /**\n   * Get flight booking context from completed subtasks\n   */ getFlightBookingContext(plan) {\n        const context = {\n            origin: null,\n            destination: null,\n            departureTime: null,\n            arrivalTime: null,\n            duration: null\n        };\n        // Extract context from the original query and plan goal\n        const queryLower = plan.originalQuery.toLowerCase();\n        const goalLower = plan.goal.toLowerCase();\n        // Extract origin and destination from query\n        if (queryLower.includes('owerri') && queryLower.includes('abuja')) {\n            context.origin = 'Owerri';\n            context.destination = 'Abuja';\n        } else if (queryLower.includes('abuja') && queryLower.includes('dubai')) {\n            context.origin = 'Abuja';\n            context.destination = 'Dubai';\n        }\n        // Extract any timing information from completed subtasks\n        const completedSubtasks = plan.subtasks.filter((task)=>task.status === 'completed' && task.result);\n        for (const task of completedSubtasks){\n            const result = JSON.stringify(task.result).toLowerCase();\n            // Look for departure times\n            const timeMatch = result.match(/(\\d{1,2}:\\d{2}(?:\\s*(?:am|pm))?)/);\n            if (timeMatch && !context.departureTime) {\n                context.departureTime = timeMatch[1];\n            }\n            // Look for duration\n            const durationMatch = result.match(/(\\d+(?:\\s*(?:hours?|hrs?|h))?(?:\\s*(?:and|\\&)?\\s*\\d+(?:\\s*(?:minutes?|mins?|m))?)?)/);\n            if (durationMatch && !context.duration) {\n                context.duration = durationMatch[1];\n            }\n        }\n        return context;\n    }\n    /**\n   * Get site-specific origin selectors\n   */ getOriginSelectors(site) {\n        const siteHost = new URL(site).hostname.toLowerCase();\n        const commonSelectors = [\n            'input[placeholder*=\"From\"]',\n            'input[name*=\"origin\"]',\n            'input[id*=\"from\"]',\n            'input[aria-label*=\"From\"]',\n            'input[data-testid*=\"origin\"]',\n            'input[data-testid*=\"from\"]'\n        ];\n        if (siteHost.includes('kayak')) {\n            return [\n                'input[placeholder*=\"From\"]',\n                'input[aria-label*=\"Flight origin\"]',\n                'input[data-testid=\"origin\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('expedia')) {\n            return [\n                'input[id*=\"flight-origin\"]',\n                'input[aria-label*=\"Leaving from\"]',\n                'input[data-testid*=\"origin\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('skyscanner')) {\n            return [\n                'input[placeholder*=\"From\"]',\n                'input[data-testid*=\"origin\"]',\n                'input[name*=\"OriginPlace\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('google')) {\n            return [\n                'input[placeholder*=\"Where from\"]',\n                'input[aria-label*=\"Where from\"]',\n                'input[jsname*=\"origin\"]',\n                ...commonSelectors\n            ];\n        }\n        return commonSelectors;\n    }\n    /**\n   * Get site-specific destination selectors\n   */ getDestinationSelectors(site) {\n        const siteHost = new URL(site).hostname.toLowerCase();\n        const commonSelectors = [\n            'input[placeholder*=\"To\"]',\n            'input[name*=\"destination\"]',\n            'input[id*=\"to\"]',\n            'input[aria-label*=\"To\"]',\n            'input[data-testid*=\"destination\"]',\n            'input[data-testid*=\"to\"]'\n        ];\n        if (siteHost.includes('kayak')) {\n            return [\n                'input[placeholder*=\"To\"]',\n                'input[aria-label*=\"Flight destination\"]',\n                'input[data-testid=\"destination\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('expedia')) {\n            return [\n                'input[id*=\"flight-destination\"]',\n                'input[aria-label*=\"Going to\"]',\n                'input[data-testid*=\"destination\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('skyscanner')) {\n            return [\n                'input[placeholder*=\"To\"]',\n                'input[data-testid*=\"destination\"]',\n                'input[name*=\"DestinationPlace\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('google')) {\n            return [\n                'input[placeholder*=\"Where to\"]',\n                'input[aria-label*=\"Where to\"]',\n                'input[jsname*=\"destination\"]',\n                ...commonSelectors\n            ];\n        }\n        return commonSelectors;\n    }\n    /**\n   * Get site-specific date selectors\n   */ getDateSelectors(site) {\n        const siteHost = new URL(site).hostname.toLowerCase();\n        const commonSelectors = [\n            'input[placeholder*=\"Departure\"]',\n            'input[name*=\"departure\"]',\n            'input[id*=\"depart\"]',\n            'input[data-testid*=\"departure\"]',\n            'button[data-testid*=\"departure\"]'\n        ];\n        if (siteHost.includes('kayak')) {\n            return [\n                'input[aria-label*=\"Start date\"]',\n                'input[data-testid=\"departure-date\"]',\n                'button[data-testid=\"departure-date\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('expedia')) {\n            return [\n                'input[id*=\"flight-departing\"]',\n                'button[data-testid*=\"departure\"]',\n                'input[aria-label*=\"Departing\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('skyscanner')) {\n            return [\n                'input[placeholder*=\"Depart\"]',\n                'button[data-testid*=\"depart\"]',\n                'input[name*=\"OutboundDate\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('google')) {\n            return [\n                'input[placeholder*=\"Departure\"]',\n                'input[aria-label*=\"Departure\"]',\n                'div[data-value*=\"departure\"]',\n                ...commonSelectors\n            ];\n        }\n        return commonSelectors;\n    }\n    /**\n   * Get site-specific search button selectors\n   */ getSearchButtonSelectors(site) {\n        const siteHost = new URL(site).hostname.toLowerCase();\n        const commonSelectors = [\n            'button[type=\"submit\"]',\n            '.search-button',\n            'button:contains(\"Search\")',\n            '[data-testid=\"submit\"]',\n            'button[aria-label*=\"Search\"]'\n        ];\n        if (siteHost.includes('kayak')) {\n            return [\n                'button[aria-label*=\"Search\"]',\n                'button[data-testid=\"submit-button\"]',\n                '.Common-Widgets-Button-ButtonPrimary',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('expedia')) {\n            return [\n                'button[data-testid*=\"search\"]',\n                'button[aria-label*=\"Search\"]',\n                '.btn-primary',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('skyscanner')) {\n            return [\n                'button[data-testid*=\"search\"]',\n                'button:contains(\"Search flights\")',\n                '.BpkButton--primary',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('google')) {\n            return [\n                'button[aria-label*=\"Search\"]',\n                'button[jsname*=\"search\"]',\n                '.VfPpkd-LgbsSe',\n                ...commonSelectors\n            ];\n        }\n        return commonSelectors;\n    }\n    /**\n   * Execute extract subtask\n   */ async executeExtractSubtask(subtask, plan) {\n        // Similar to navigate but with specific extraction focus\n        return this.executeNavigateSubtask(subtask, plan);\n    }\n    /**\n   * Execute analyze subtask - synthesize gathered information\n   */ async executeAnalyzeSubtask(subtask, plan, browsingConfig) {\n        try {\n            console.log(`[Smart Browsing] 🧠 Analyzing gathered data...`);\n            // Collect all data from completed subtasks\n            const gatheredData = plan.subtasks.filter((task)=>task.status === 'completed' && task.result).map((task)=>({\n                    type: task.type,\n                    description: task.description,\n                    query: task.query,\n                    data: task.result\n                }));\n            if (gatheredData.length === 0) {\n                return {\n                    success: false,\n                    error: 'No data available for analysis'\n                };\n            }\n            // Use AI to analyze and synthesize the data\n            const model = browsingConfig.browsing_models[0];\n            if (!model) {\n                return {\n                    success: false,\n                    error: 'No AI model available for analysis'\n                };\n            }\n            const analysisPrompt = this.buildAnalysisPrompt(plan.originalQuery, gatheredData);\n            const aiResult = await this.callAIForAnalysis(analysisPrompt, model);\n            if (aiResult.success && aiResult.content) {\n                return {\n                    success: true,\n                    data: {\n                        analysis: aiResult.content,\n                        sourceData: gatheredData\n                    }\n                };\n            } else {\n                return {\n                    success: false,\n                    error: aiResult.error || 'Analysis failed'\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Analysis failed'\n            };\n        }\n    }\n    /**\n   * Calculate progress percentage\n   */ calculateProgress(plan) {\n        const totalTasks = plan.subtasks.length;\n        const completedTasks = plan.completedSubtasks.length;\n        return Math.round(completedTasks / totalTasks * 100);\n    }\n    /**\n   * Log progress update\n   */ logProgress(plan) {\n        const completed = plan.completedSubtasks.length;\n        const failed = plan.failedSubtasks.length;\n        const total = plan.subtasks.length;\n        const remaining = total - completed - failed;\n        console.log(`[Smart Browsing] 📊 Progress: ${plan.progress}% (${completed}/${total} completed, ${failed} failed, ${remaining} remaining)`);\n    }\n    /**\n   * Build context-aware query using results from previous subtasks\n   */ buildContextualQuery(subtask, plan) {\n        let contextualQuery = subtask.query;\n        // Get context from completed subtasks\n        const completedSubtasks = plan.subtasks.filter((task)=>task.status === 'completed' && task.result && task.id !== subtask.id);\n        if (completedSubtasks.length === 0) {\n            return contextualQuery;\n        }\n        console.log(`[Smart Browsing] 🧠 Building contextual query from ${completedSubtasks.length} completed subtasks`);\n        // Extract key information from previous results\n        const contextInfo = [];\n        for (const completedTask of completedSubtasks){\n            const result = completedTask.result;\n            // Extract specific information based on task type and content\n            if (completedTask.description.toLowerCase().includes('flight') && completedTask.description.toLowerCase().includes('earliest')) {\n                // Extract flight times, airlines, etc.\n                const flightInfo = this.extractFlightInfo(result);\n                if (flightInfo) {\n                    contextInfo.push(flightInfo);\n                }\n            }\n            if (completedTask.description.toLowerCase().includes('duration')) {\n                // Extract duration information\n                const durationInfo = this.extractDurationInfo(result);\n                if (durationInfo) {\n                    contextInfo.push(durationInfo);\n                }\n            }\n            // Extract arrival times for connecting flights\n            if (completedTask.description.toLowerCase().includes('arrive')) {\n                const arrivalInfo = this.extractArrivalInfo(result);\n                if (arrivalInfo) {\n                    contextInfo.push(arrivalInfo);\n                }\n            }\n        }\n        // Enhance the query with context\n        if (contextInfo.length > 0) {\n            const context = contextInfo.join(', ');\n            // Build more specific contextual queries based on subtask type\n            if (subtask.description.toLowerCase().includes('connecting') || subtask.description.toLowerCase().includes('after arrival')) {\n                // For connecting flights, calculate departure time based on arrival + buffer\n                const arrivalTime = this.extractTimeFromContext(contextInfo);\n                if (arrivalTime) {\n                    const departureTime = this.calculateConnectingFlightTime(arrivalTime);\n                    contextualQuery = `${subtask.query} departing after ${departureTime}`;\n                } else {\n                    contextualQuery = `${subtask.query} departing after ${context}`;\n                }\n            } else if (subtask.description.toLowerCase().includes('duration') && contextInfo.some((info)=>info.includes('flight'))) {\n                // For duration queries, be more specific about which flight\n                const flightInfo = contextInfo.find((info)=>info.includes('flight'));\n                contextualQuery = `${subtask.query} for ${flightInfo}`;\n            } else if (subtask.description.toLowerCase().includes('earliest') && contextInfo.some((info)=>info.includes('arrives'))) {\n                // For earliest flight queries after knowing arrival time\n                const arrivalInfo = contextInfo.find((info)=>info.includes('arrives'));\n                contextualQuery = `${subtask.query} after ${arrivalInfo}`;\n            } else {\n                contextualQuery = `${subtask.query} (context: ${context})`;\n            }\n            console.log(`[Smart Browsing] 🎯 Enhanced query with context: \"${contextualQuery}\"`);\n        }\n        return contextualQuery;\n    }\n    /**\n   * Extract flight information from search results\n   */ extractFlightInfo(result) {\n        if (!result) return null;\n        try {\n            const resultStr = JSON.stringify(result).toLowerCase();\n            // Look for flight times (e.g., \"06:30\", \"6:30 am\", etc.)\n            const timePattern = /(\\d{1,2}:\\d{2}(?:\\s*(?:am|pm))?)/gi;\n            const times = resultStr.match(timePattern);\n            // Look for airlines\n            const airlinePattern = /(arik air|air peace|dana air|azman air|emirates|qatar|turkish)/gi;\n            const airlines = resultStr.match(airlinePattern);\n            if (times && times.length > 0) {\n                const earliestTime = times[0];\n                const airline = airlines ? airlines[0] : '';\n                return `earliest flight ${earliestTime}${airline ? ` on ${airline}` : ''}`;\n            }\n        } catch (error) {\n            console.warn('[Smart Browsing] Error extracting flight info:', error);\n        }\n        return null;\n    }\n    /**\n   * Extract duration information from search results\n   */ extractDurationInfo(result) {\n        if (!result) return null;\n        try {\n            const resultStr = JSON.stringify(result).toLowerCase();\n            // Look for duration patterns (e.g., \"2 hours 30 minutes\", \"2h 30m\", etc.)\n            const durationPattern = /(\\d+(?:\\s*(?:hours?|hrs?|h))?(?:\\s*(?:and|\\&)?\\s*\\d+(?:\\s*(?:minutes?|mins?|m))?)?)/gi;\n            const durations = resultStr.match(durationPattern);\n            if (durations && durations.length > 0) {\n                return `duration ${durations[0]}`;\n            }\n        } catch (error) {\n            console.warn('[Smart Browsing] Error extracting duration info:', error);\n        }\n        return null;\n    }\n    /**\n   * Extract arrival time information from search results\n   */ extractArrivalInfo(result) {\n        if (!result) return null;\n        try {\n            const resultStr = JSON.stringify(result).toLowerCase();\n            // Look for arrival times\n            const arrivalPattern = /(?:arrives?|arrival|landing)(?:\\s*(?:at|in|by))?\\s*(\\d{1,2}:\\d{2}(?:\\s*(?:am|pm))?)/gi;\n            const arrivals = resultStr.match(arrivalPattern);\n            if (arrivals && arrivals.length > 0) {\n                return `arrives ${arrivals[0]}`;\n            }\n        } catch (error) {\n            console.warn('[Smart Browsing] Error extracting arrival info:', error);\n        }\n        return null;\n    }\n    /**\n   * Extract time information from context\n   */ extractTimeFromContext(contextInfo) {\n        for (const info of contextInfo){\n            const timeMatch = info.match(/(\\d{1,2}:\\d{2}(?:\\s*(?:am|pm))?)/i);\n            if (timeMatch) {\n                return timeMatch[1];\n            }\n        }\n        return null;\n    }\n    /**\n   * Calculate connecting flight departure time with buffer\n   */ calculateConnectingFlightTime(arrivalTime) {\n        try {\n            // Parse the arrival time\n            const timeMatch = arrivalTime.match(/(\\d{1,2}):(\\d{2})(?:\\s*(am|pm))?/i);\n            if (!timeMatch) return arrivalTime;\n            let hours = parseInt(timeMatch[1]);\n            const minutes = parseInt(timeMatch[2]);\n            const ampm = timeMatch[3]?.toLowerCase();\n            // Convert to 24-hour format\n            if (ampm === 'pm' && hours !== 12) {\n                hours += 12;\n            } else if (ampm === 'am' && hours === 12) {\n                hours = 0;\n            }\n            // Add 2-hour buffer for international connections, 1 hour for domestic\n            const bufferHours = 2; // Assume international for safety\n            hours += bufferHours;\n            // Handle day overflow\n            if (hours >= 24) {\n                hours -= 24;\n            }\n            // Format back to readable time\n            const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;\n            const displayAmPm = hours >= 12 ? 'PM' : 'AM';\n            return `${displayHours}:${String(minutes).padStart(2, '0')} ${displayAmPm}`;\n        } catch (error) {\n            console.warn('[Smart Browsing] Error calculating connecting flight time:', error);\n            return arrivalTime;\n        }\n    }\n    /**\n   * Enhance subtask query with dynamic context-aware modifications\n   */ enhanceSubtaskQuery(subtask, plan) {\n        const enhancedSubtask = {\n            ...subtask\n        };\n        // Get context from completed subtasks\n        const completedSubtasks = plan.subtasks.filter((task)=>task.status === 'completed' && task.result && task.id !== subtask.id);\n        if (completedSubtasks.length === 0) {\n            return enhancedSubtask;\n        }\n        // Enhance search terms based on context\n        const contextualTerms = [];\n        for (const completedTask of completedSubtasks){\n            const result = completedTask.result;\n            // Extract specific information for search term enhancement\n            if (completedTask.description.toLowerCase().includes('earliest flight')) {\n                const flightInfo = this.extractFlightInfo(result);\n                if (flightInfo) {\n                    // Add specific flight details to search terms\n                    contextualTerms.push(`${subtask.query} ${flightInfo}`);\n                    contextualTerms.push(`${subtask.query} after ${flightInfo}`);\n                }\n            }\n            if (completedTask.description.toLowerCase().includes('duration')) {\n                const durationInfo = this.extractDurationInfo(result);\n                if (durationInfo) {\n                    // Use duration info for more specific searches\n                    contextualTerms.push(`${subtask.query} ${durationInfo}`);\n                }\n            }\n        }\n        // Add contextual terms to search terms\n        if (contextualTerms.length > 0) {\n            enhancedSubtask.searchTerms = [\n                ...enhancedSubtask.searchTerms || [],\n                ...contextualTerms\n            ];\n            console.log(`[Smart Browsing] 🎯 Enhanced subtask with ${contextualTerms.length} contextual search terms`);\n        }\n        return enhancedSubtask;\n    }\n    /**\n   * Execute complex automation search for flight booking and form filling\n   */ async executeComplexAutomationSearch(subtask, plan, query) {\n        console.log(`[Smart Browsing] 🤖 Starting complex automation search for: ${query}`);\n        // Determine the best sites to use based on the query type\n        const targetSites = this.getTargetSitesForQuery(query, subtask);\n        const errors = [];\n        for (const site of targetSites){\n            try {\n                console.log(`[Smart Browsing] 🌐 Attempting automation on: ${site}`);\n                // Build automation workflow based on the task type\n                const workflow = this.buildTaskSpecificWorkflow(subtask, plan, site, query);\n                const result = await this.browserlessService.executeComplexWorkflow(site, workflow);\n                if (result && result.data) {\n                    console.log(`[Smart Browsing] ✅ Complex automation successful on: ${site}`);\n                    return {\n                        success: true,\n                        data: result.data\n                    };\n                } else {\n                    const errorMsg = `Automation on ${site} returned no data`;\n                    console.log(`[Smart Browsing] ❌ ${errorMsg}`);\n                    errors.push(errorMsg);\n                }\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n                const errorMsg = `Automation failed on ${site}: ${errorMessage}`;\n                console.log(`[Smart Browsing] ❌ ${errorMsg}`);\n                console.log(`[Smart Browsing] Error details:`, error);\n                errors.push(errorMsg);\n            }\n        }\n        // If all automation attempts failed, try a final fallback search\n        console.log(`[Smart Browsing] 🔄 All automation failed, trying final fallback search`);\n        try {\n            const fallbackQuery = this.extractSearchQueryFromSubtask(subtask);\n            console.log(`[Smart Browsing] 🔍 Final fallback search query: ${fallbackQuery}`);\n            const fallbackResult = await this.browserlessService.searchAndExtractUnblocked(fallbackQuery);\n            if (fallbackResult.success && fallbackResult.data) {\n                console.log(`[Smart Browsing] ✅ Final fallback search successful`);\n                return {\n                    success: true,\n                    data: fallbackResult.data\n                };\n            } else {\n                errors.push(`Final fallback search failed: ${fallbackResult.error || 'No data'}`);\n            }\n        } catch (fallbackError) {\n            const fallbackErrorMsg = fallbackError instanceof Error ? fallbackError.message : 'Unknown fallback error';\n            errors.push(`Final fallback search error: ${fallbackErrorMsg}`);\n            console.log(`[Smart Browsing] ❌ Final fallback error:`, fallbackError);\n        }\n        return {\n            success: false,\n            error: `All automation and fallback attempts failed. Errors: ${errors.join('; ')}`\n        };\n    }\n    /**\n   * Get target sites based on query type and content\n   */ getTargetSitesForQuery(query, subtask) {\n        const description = (subtask.description || '').toLowerCase();\n        const queryLower = query.toLowerCase();\n        // Flight booking sites\n        if (queryLower.includes('flight') || queryLower.includes('airline') || queryLower.includes('departure') || queryLower.includes('arrival')) {\n            return [\n                'https://www.kayak.com',\n                'https://www.expedia.com',\n                'https://www.skyscanner.com',\n                'https://www.google.com/flights'\n            ];\n        }\n        // Hotel booking sites\n        if (queryLower.includes('hotel') || queryLower.includes('accommodation') || queryLower.includes('booking') || queryLower.includes('stay')) {\n            return [\n                'https://www.booking.com',\n                'https://www.expedia.com',\n                'https://www.hotels.com',\n                'https://www.kayak.com'\n            ];\n        }\n        // Restaurant reservation sites\n        if (queryLower.includes('restaurant') || queryLower.includes('reservation') || queryLower.includes('dining') || queryLower.includes('table')) {\n            return [\n                'https://www.opentable.com',\n                'https://www.yelp.com',\n                'https://www.resy.com'\n            ];\n        }\n        // Shopping sites\n        if (queryLower.includes('buy') || queryLower.includes('purchase') || queryLower.includes('shop') || queryLower.includes('product')) {\n            return [\n                'https://www.amazon.com',\n                'https://www.google.com/shopping',\n                'https://www.ebay.com'\n            ];\n        }\n        // Job search sites\n        if (queryLower.includes('job') || queryLower.includes('career') || queryLower.includes('employment') || queryLower.includes('hiring')) {\n            return [\n                'https://www.linkedin.com/jobs',\n                'https://www.indeed.com',\n                'https://www.glassdoor.com'\n            ];\n        }\n        // Default to general search engines for complex automation\n        return [\n            'https://www.google.com',\n            'https://www.bing.com',\n            'https://duckduckgo.com'\n        ];\n    }\n    /**\n   * Build task-specific workflow based on the query and site\n   */ buildTaskSpecificWorkflow(subtask, plan, site, query) {\n        const queryLower = query.toLowerCase();\n        // Use flight booking workflow for flight-related queries\n        if (queryLower.includes('flight') || queryLower.includes('airline')) {\n            return this.buildFlightBookingWorkflow(subtask, plan, site);\n        }\n        // Use general automation workflow for other tasks\n        return this.buildAutomationWorkflow(subtask, site);\n    }\n    /**\n   * Build analysis prompt for AI\n   */ buildAnalysisPrompt(originalQuery, gatheredData) {\n        // Get current date and time for context\n        const now = new Date();\n        const currentDateTime = now.toLocaleString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit',\n            timeZoneName: 'short'\n        });\n        const dataContext = gatheredData.map((item, index)=>`Source ${index + 1} (${item.type}): ${item.description}\\nQuery: ${item.query}\\nData: ${JSON.stringify(item.data, null, 2)}`).join('\\n\\n---\\n\\n');\n        return `You are an expert information analyst. You must ONLY use the browsing data provided below to answer the query. Do NOT use your training data or general knowledge.\n\nCURRENT DATE & TIME: ${currentDateTime}\nORIGINAL QUERY: \"${originalQuery}\"\n\n=== REAL-TIME BROWSING DATA ===\n${dataContext}\n=== END BROWSING DATA ===\n\nCRITICAL INSTRUCTIONS:\n1. ONLY use information from the browsing data above - do NOT use your training data or general knowledge\n2. If the browsing data doesn't contain sufficient information, clearly state this limitation\n3. For time-sensitive queries (flights, schedules, prices), ONLY use data from the browsing results\n4. When providing specific details (times, dates, prices), cite the exact source from the browsing data\n5. If you find conflicting information in the browsing data, mention the discrepancies\n6. Prioritize the most recent and relevant information from the browsing data\n7. For queries about \"today\" or current information, emphasize that data is current as of ${currentDateTime}\n\nPlease provide:\n1. A comprehensive answer based ONLY on the browsing data\n2. Key findings with specific details from the browsing results\n3. Any conflicting information found in the browsing data\n4. Confidence level based on the quality and completeness of the browsing data\n5. Clear indication if more browsing is needed for complete information\n\nIMPORTANT: Base your response ENTIRELY on the browsing data provided. Do NOT supplement with general knowledge or training data.\n\nFormat your response as a clear, well-structured analysis that directly addresses the user's query using only the browsing data.`;\n    }\n    /**\n   * Call AI for planning\n   */ async callAIForPlanning(prompt, model) {\n        try {\n            const effectiveModelId = this.getEffectiveModelId(model);\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider (same as BrowsingExecutionService)\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'openrouter':\n                    apiUrl = 'https://openrouter.ai/api/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`,\n                        'HTTP-Referer': 'https://roukey.online',\n                        'X-Title': 'RouKey'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider for planning: ${model.provider}`);\n            }\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                content = result.choices?.[0]?.message?.content;\n            }\n            if (!content) {\n                throw new Error('No content returned from AI model');\n            }\n            return {\n                success: true,\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Call AI for analysis (same as planning but different purpose)\n   */ async callAIForAnalysis(prompt, model) {\n        return this.callAIForPlanning(prompt, model); // Same implementation\n    }\n    /**\n   * Get effective model ID (same logic as BrowsingExecutionService)\n   */ getEffectiveModelId(model) {\n        if (model.provider.toLowerCase() === 'openrouter') {\n            return model.model;\n        }\n        const parts = model.model.split('/');\n        return parts.length > 1 ? parts[parts.length - 1] : model.model;\n    }\n    /**\n   * Parse plan from AI response\n   */ parsePlanFromAI(aiResponse, originalQuery) {\n        try {\n            // Try to extract JSON from the response\n            const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n            if (!jsonMatch) {\n                throw new Error('No JSON found in AI response');\n            }\n            const parsed = JSON.parse(jsonMatch[0]);\n            if (!parsed.goal || !parsed.subtasks || !Array.isArray(parsed.subtasks)) {\n                throw new Error('Invalid plan structure from AI');\n            }\n            // Convert AI subtasks to our format\n            const subtasks = parsed.subtasks.map((task, index)=>({\n                    id: task.id || `ai_task_${Date.now()}_${index}`,\n                    type: task.type || 'search',\n                    description: task.description || `Task ${index + 1}`,\n                    query: task.query || originalQuery,\n                    status: 'pending',\n                    priority: task.priority || 5,\n                    attempts: 0,\n                    maxAttempts: 3,\n                    searchTerms: task.searchTerms || [],\n                    expectedInfo: task.expectedInfo || ''\n                }));\n            return {\n                goal: parsed.goal,\n                subtasks\n            };\n        } catch (error) {\n            console.warn('[Smart Browsing] Failed to parse AI plan, using fallback:', error);\n            return {\n                goal: `Find information about: ${originalQuery}`,\n                subtasks: this.createFallbackPlan(originalQuery, 'search')\n            };\n        }\n    }\n    /**\n   * Synthesize final result from all gathered data\n   */ async synthesizeFinalResult(plan, browsingConfig) {\n        try {\n            // Find the analysis result if available\n            const analysisTask = plan.subtasks.find((task)=>task.type === 'analyze' && task.status === 'completed' && task.result);\n            if (analysisTask && analysisTask.result?.analysis) {\n                return analysisTask.result.analysis;\n            }\n            // If no analysis task, create a summary from all completed tasks\n            const completedTasks = plan.subtasks.filter((task)=>task.status === 'completed' && task.result);\n            if (completedTasks.length === 0) {\n                return `No information was successfully gathered for the query: \"${plan.originalQuery}\"`;\n            }\n            // Create a basic summary\n            let summary = `Based on browsing research for \"${plan.originalQuery}\":\\n\\n`;\n            completedTasks.forEach((task, index)=>{\n                summary += `${index + 1}. ${task.description}:\\n`;\n                if (task.result?.results && Array.isArray(task.result.results)) {\n                    task.result.results.slice(0, 3).forEach((result)=>{\n                        summary += `   • ${result.title || 'Result'}\\n`;\n                    });\n                } else if (typeof task.result === 'string') {\n                    summary += `   ${task.result.substring(0, 200)}...\\n`;\n                }\n                summary += '\\n';\n            });\n            return summary;\n        } catch (error) {\n            console.error('[Smart Browsing] Error synthesizing final result:', error);\n            return `Research completed for \"${plan.originalQuery}\" but encountered errors in synthesis. Please check the individual results.`;\n        }\n    }\n    /**\n   * Log the browsing plan for debugging\n   */ logPlan(plan) {\n        console.log(`[Smart Browsing] 📋 BROWSING PLAN:`);\n        console.log(`[Smart Browsing] Goal: ${plan.goal}`);\n        console.log(`[Smart Browsing] Subtasks:`);\n        plan.subtasks.forEach((subtask, index)=>{\n            console.log(`[Smart Browsing]   ${index + 1}. [${subtask.type.toUpperCase()}] ${subtask.description}`);\n            console.log(`[Smart Browsing]      Query: \"${subtask.query}\"`);\n            console.log(`[Smart Browsing]      Priority: ${subtask.priority}, Status: ${subtask.status}`);\n            if (subtask.searchTerms && subtask.searchTerms.length > 0) {\n                console.log(`[Smart Browsing]      Alt terms: ${subtask.searchTerms.join(', ')}`);\n            }\n        });\n    }\n    /**\n   * Create or reuse browser session for complex workflows\n   */ async ensureBrowserSession(plan, initialUrl) {\n        // Check if we have an existing session that's still valid\n        if (plan.sessionInfo && plan.sessionInfo.reconnectUrl) {\n            const timeSinceLastUse = Date.now() - new Date(plan.sessionInfo.lastUsed).getTime();\n            const sessionTimeout = 10 * 60 * 1000; // 10 minutes\n            if (timeSinceLastUse < sessionTimeout) {\n                console.log(`[Smart Browsing] 🔄 Reusing existing browser session: ${plan.sessionInfo.sessionId}`);\n                // Update last used time\n                plan.sessionInfo.lastUsed = new Date();\n                return {\n                    sessionId: plan.sessionInfo.sessionId,\n                    reconnectUrl: plan.sessionInfo.reconnectUrl\n                };\n            } else {\n                console.log(`[Smart Browsing] ⏰ Session expired, creating new session`);\n            }\n        }\n        // Create new session\n        console.log(`[Smart Browsing] 🆕 Creating new browser session`);\n        const session = await this.browserlessService.createBrowsingSession(initialUrl || 'https://www.google.com', {\n            timeout: 600000,\n            humanLike: true\n        });\n        // Store session info in plan\n        plan.sessionInfo = {\n            sessionId: session.sessionId,\n            reconnectUrl: session.reconnectUrl,\n            lastUsed: new Date(),\n            activeWorkflow: true\n        };\n        console.log(`[Smart Browsing] ✅ Created browser session: ${session.sessionId}`);\n        return session;\n    }\n    /**\n   * Check if subtask requires persistent session\n   */ requiresPersistentSession(subtask) {\n        const description = subtask.description.toLowerCase();\n        const query = subtask.query.toLowerCase();\n        // Tasks that benefit from session persistence\n        const sessionKeywords = [\n            'form',\n            'booking',\n            'reservation',\n            'login',\n            'multi-step',\n            'workflow',\n            'navigation',\n            'complex',\n            'automation'\n        ];\n        // Flight booking always benefits from session persistence\n        const flightBookingKeywords = [\n            'flight',\n            'airline',\n            'booking',\n            'search flight',\n            'travel'\n        ];\n        return sessionKeywords.some((keyword)=>description.includes(keyword) || query.includes(keyword)) || flightBookingKeywords.some((keyword)=>description.includes(keyword) || query.includes(keyword));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts\n");

/***/ })

};
;