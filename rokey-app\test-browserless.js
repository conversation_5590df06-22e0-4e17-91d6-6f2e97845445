// Test script to verify Browserless API key and endpoint
const fetch = require('node-fetch');

async function testBrowserlessAPI() {
  const apiKey = '2ScDdfCuVNKkYC3f324cbad0915d0c5128dc1805683dcf963';
  let endpoint = `https://production-sfo.browserless.io/chromium/bql?token=${apiKey}`;
  endpoint += `&stealth=true&humanlike=true&blockAds=true&blockConsentModals=true`;
  endpoint += `&timeout=60000`; // Remove residential proxy for now

  const stealthQuery = `
    mutation StealthWorkflow {
      navigation: goto(url: "https://www.google.com/flights") {
        status
        time
      }

      waitForLoad: waitForTimeout(time: 3000) {
        time
      }

      initialTitle: title {
        title
      }

      solveCaptcha: solve(type: recaptcha, timeout: 45000) {
        found
        solved
        time
      }

      waitAfterCaptcha: waitForTimeout(time: 5000) {
        time
      }

      finalTitle: title {
        title
      }

      pageContent: html {
        html
      }
    }
  `;

  try {
    console.log('Testing Browserless API...');
    console.log('Endpoint:', endpoint);
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'RouKey-Browser-Test/1.0'
      },
      body: JSON.stringify({
        query: stealthQuery,
        variables: {}
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      return;
    }

    const result = await response.json();
    console.log('Success! Response:', JSON.stringify(result, null, 2));

  } catch (error) {
    console.error('Test failed:', error);
  }
}

testBrowserlessAPI();
